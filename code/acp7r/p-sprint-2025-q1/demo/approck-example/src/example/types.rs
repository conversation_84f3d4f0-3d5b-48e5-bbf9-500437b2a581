#[granite::gtype(ts_from = "./modλ.mts", RsAll, TsAll)]
pub type SharedType = super::SharedType;

#[granite::gtype(RsAll, TsAll)]
pub struct StructUnit;

#[granite::gtype(RsAll, TsAll)]
pub struct MyStruct1(String);

#[granite::gtype(RsAll, TsAll)]
pub struct MyStructTuple(String, i32, Option<i32>);

#[granite::gtype(RsAll, TsAll)]
pub struct StructNamed {
    #[gtype(default="John Doe"; max=10; trim=both; no_empty;)]
    pub name: String,

    #[gtype(default=21; max=120; min=1;)]
    pub age: i8,

    #[gtype(zero_to_none; <(default=10; max=10; min=1;)>)]
    pub foo1: Option<i8>,

    #[gtype(<(max_items=120; <(max=128;), (default=32; max=1024;)>)>)]
    pub bar: Option<HashMap<String, i32>>,

    #[gtype(default=10; max=12; min=1;)]
    pub shoe_size: u8,

    //#[gtype(zero_to_none; <(min=5; max=50;)>)]
    pub hat_size: Option<u8>,

    pub nested_map: HashMap<String, Option<HashMap<String, Option<i32>>>>,

    pub shared_type: SharedType,

    pub named_mini_foos: Vec<NamedMiniFoo>,
}

#[granite::gtype(RsAll, TsAll)]
pub struct NamedMiniFoo {
    #[gtype(default="Jane Doe"; max=64; trim=both; no_empty;)]
    pub name: String,

    #[gtype(default=21; max=120; min=1;)]
    pub age: i8,

    #[gtype(zero_to_none; <(min=5; max=50;)>)]
    pub hat_size: Option<u8>,

    #[gtype(max_items=10; <(max=20;), (default="Foobar"; max=1024;)>)]
    pub wish_list: HashMap<String, String>,
}

#[granite::gtype(RsAll, TsAll)]
pub enum Enum {
    Unit1,
    Unit2,
    Tuple1(i32),
    Tuple2Opt(i32, Option<String>),
    Tuple3(i32, String, String),
    Named1 {
        #[gtype(default="Foo Bar Baz"; min=5; max=10;)]
        struct_named: StructNamed,
    },
    Named2 {
        #[gtype(default="John Doe"; max=10; trim=both; no_empty;)]
        name: String,
        #[gtype(default=21; max=120; min=1;)]
        age: i32,
        #[gtype(zero_to_none; <(default=10; max=10; min=1;)>)]
        food: Option<i32>,
    },
}

#[granite::gtype(RsAll, TsAll)]
pub struct WithSharedType {
    pub shared_type: SharedType,
}

#[granite::gtype(RsAll, TsAll)]
pub struct JustJson(JsonValue);
