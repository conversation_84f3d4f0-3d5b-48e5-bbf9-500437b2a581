use maud::{Markup, html};

#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    use super::render_dashboard_table;

    pub async fn request(ui: Document) -> Response {
        ui.add_body(render_dashboard_table());

        Response::HTML(ui.into())
    }
}

fn render_dashboard_table() -> Markup {
    let headers = vec![
        "Host Name",
        "AccountKey",
        "Region",
        "Instance",
        "State",
        "Hours Ago",
        "Disk Used %",
        "Disk Free",
    ];

    let rows = vec![
        vec![
            "ams.tmtdev.net",
            "tmtk",
            "us-east-1",
            "i-03ce7300f4fa58892",
            "running",
            "0.03",
            "98.14%",
            "17.71 GB",
        ],
        vec![
            "deep.acp7.net",
            "acp7.net",
            "us-east-2",
            "i-0f473b39a588dd1a4",
            "running",
            "0.03",
            "97.10%",
            "39.84 GB",
        ],
        vec![
            "daas-rr-0.daas7.net",
            "daas7.net",
            "us-east-2",
            "i-0b894acd85b078bf8",
            "running",
            "0.03",
            "86.21%",
            "32.63 GB",
        ],
        vec![
            "vpn.acp7.net",
            "acp7.net",
            "us-east-2",
            "i-02bfdb50288dfd1cc",
            "running",
            "0.03",
            "81.66%",
            "1.39 GB",
        ],
        vec![
            "ace.acp7.net",
            "acp7.net",
            "us-east-2",
            "i-0778d6c283819291e",
            "running",
            "0.03",
            "69.35%",
            "153.25 GB",
        ],
    ];

    let cert_headers = vec!["CommonName", "Expires", "Instructions"];

    let cert_rows = vec![
        vec![
            "*.premeetingmaterials.com",
            "Aug 14, 2024",
            "This certificate is presently installed on build7.acp7.net:DaaS-TMT-0/LOCAL.yaml. After updating it, ./acp build; ./acp deploy --run;",
        ],
        vec![
            "www.wowmyprospect.com",
            "Feb 18, 2025",
            "1 year; Install to amstmt.tmtdev.net DNS is located in TMT AWS",
        ],
        vec![
            "reseller.technologymarketingtoolkit.com",
            "Feb 20, 2025",
            "1 year; Install to amstmt.tmtdev.net DNS is located in cloudflare (tmt). ssh root@192.168.2.40",
        ],
    ];

    let backup_headers = vec![
        "ID❓",
        "Canonical Database",
        "ID❓",
        "Actual Backup",
        "Hours Ago",
        "Duration",
    ];

    let backup_rows = vec![
        vec![
            "30",
            "AMSRR_0 (ams.tmtdev.net)",
            "56959",
            "AMSRR_0 (ams.tmtdev.net)",
            "11.92 hrs",
            "41 sec",
        ],
        vec![
            "31",
            "AMSTMT_0 (ams.tmtdev.net)",
            "56960",
            "AMSTMT_0 (ams.tmtdev.net)",
            "11.91 hrs",
            "315 sec",
        ],
        vec![
            "32",
            "AMSTRJ_0 (ams.tmtdev.net)",
            "56961",
            "AMSTRJ_0 (ams.tmtdev.net)",
            "11.82 hrs",
            "51 sec",
        ],
    ];

    let queue_headers = vec![
        "App_GSID", "App_XSID", "1m", "5m", "15m", "1h", "4h", "1d", "3d",
    ];

    let queue_rows = vec![
        vec!["GiftHopper", "ACP7", "-", "-", "-", "-", "-", "19", "25"],
        vec!["Smart", "ACP7", "-", "-", "-", "-", "5", "6", "6"],
        vec!["AppCoveWeb", "ACP7", "-", "-", "-", "-", "-", "-", "-"],
        vec!["BRV-Alpha", "ACP7", "-", "-", "-", "-", "-", "-", "-"],
    ];

    let async_headers = vec![
        "App_GSID", "Callable", "Enabled", "Date", "Pending", "Running", "Success", "Failure",
    ];

    let async_rows = vec![
        vec![
            "BRV-Alpha",
            "App.VM4.InfusionSoft.ImportExportJob",
            "Enabled",
            "2024-06-12",
            "0",
            "0",
            "96",
            "0",
        ],
        vec![
            "DaaS-CEOWAR",
            "App.InfusionSoft_Pull",
            "Enabled",
            "2024-06-16",
            "0",
            "0",
            "1436",
            "2",
        ],
        vec![
            "DaaS-TRJ",
            "App.InfusionSoft_Pull",
            "Enabled",
            "2024-06-14",
            "0",
            "0",
            "1435",
            "4",
        ],
    ];

    html! {
        html {
            head {
                title { "Dashboard" }
                link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/5.1.3/css/bootstrap.min.css" {}
                script src="https://code.jquery.com/jquery-3.6.0.min.js" {}
                script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" {}
            }
            body {
                nav class="navbar navbar-expand-lg navbar-dark bg-dark" {
                    div class="container-fluid" {
                        a class="navbar-brand" href="#" { "Dashboard" }
                        button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation" {
                            span class="navbar-toggler-icon" {}
                        }
                        div class="collapse navbar-collapse" id="navbarNav" {
                            ul class="navbar-nav" {
                                li class="nav-item" {
                                    a class="nav-link active" aria-current="page" href="#instances" { "Instances" }
                                }
                                li class="nav-item" {
                                    a class="nav-link" href="#certificates" { "Certificates" }
                                }
                                li class="nav-item" {
                                    a class="nav-link" href="#backups" { "Backups" }
                                }
                                li class="nav-item" {
                                    a class="nav-link" href="#queue" { "Queue" }
                                }
                                li class="nav-item" {
                                    a class="nav-link" href="#async" { "Async Stats" }
                                }
                            }
                        }
                    }
                }
                div class="container mt-4" {
                    h1 class="text-center mb-4" { "ACE2 Dashboard" }
                    h2 id="instances" class="mt-5" { "Server Instances" }
                    table class="table table-striped table-hover" {
                        thead {
                            tr {
                                @for header in &headers {
                                    th { (header) }
                                }
                            }
                        }
                        tbody {
                            @for row in &rows {
                                tr {
                                    @for cell in row {
                                        td { (cell) }
                                    }
                                }
                            }
                        }
                    }
                    h2 id="certificates" class="mt-5" { "Manually Updated Certificates" }
                    table class="table table-striped table-hover" {
                        thead {
                            tr {
                                @for header in &cert_headers {
                                    th { (header) }
                                }
                            }
                        }
                        tbody {
                            @for row in &cert_rows {
                                tr {
                                    @for cell in row {
                                        td { (cell) }
                                    }
                                }
                            }
                        }
                    }
                    h2 id="backups" class="mt-5" { "Most Recent Database Backups" }
                    table class="table table-striped table-hover" {
                        thead {
                            tr {
                                @for header in &backup_headers {
                                    th { (header) }
                                }
                            }
                        }
                        tbody {
                            @for row in &backup_rows {
                                tr {
                                    @for cell in row {
                                        td { (cell) }
                                    }
                                }
                            }
                        }
                    }
                    h2 id="queue" class="mt-5" { "Message Queue Status" }
                    table class="table table-striped table-hover" {
                        thead {
                            tr {
                                @for header in &queue_headers {
                                    th { (header) }
                                }
                            }
                        }
                        tbody {
                            @for row in &queue_rows {
                                tr {
                                    @for cell in row {
                                        td { (cell) }
                                    }
                                }
                            }
                        }
                    }
                    h2 id="async" class="mt-5" { "Async Stats" }
                    table class="table table-striped table-hover" {
                        thead {
                            tr {
                                @for header in &async_headers {
                                    th { (header) }
                                }
                            }
                        }
                        tbody {
                            @for row in &async_rows {
                                tr {
                                    @for cell in row {
                                        td { (cell) }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
