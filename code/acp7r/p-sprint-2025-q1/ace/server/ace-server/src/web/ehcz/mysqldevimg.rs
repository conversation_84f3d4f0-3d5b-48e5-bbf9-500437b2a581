#[approck::http(GET /ehcz/mysqldevimg?app_name=String; AUTH None; return Bytes|Empty;)]
pub mod mysqldevimg {

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;

        let _developer =
            match crate::web::ehcz::authenticate_developer(username, password, app).await {
                Ok(developer) => developer,
                // TODO: handle this error better?
                Err(e) => {
                    approck::error!("Error authenticating developer: {:#?}", e);
                    let response = Empty {
                        status: approck::StatusCode::UNAUTHORIZED,
                        ..Default::default()
                    };
                    return Ok(Response::Empty(response));
                }
            };

        // TODO: check if the developer has access to this app
        let app_name = qs.app_name;

        let mysqldevimg_list = match ace_graph::mysqldevimg::select(
            &ace_graph::MysqlDevImgFilter::All,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(mysqldevimg_list) => mysqldevimg_list,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        // find the mysqldevimg for this developer
        let mysqldevimg = match mysqldevimg_list.iter().find(|a| a.name == app_name) {
            Some(mysqldevimg) => mysqldevimg,
            None => {
                return Err(granite::from_error_stack!());
            }
        };

        let file_path = &mysqldevimg.sql_path;

        let contents = tokio::fs::read(file_path).await?;
        let rval = Bytes::new(contents);

        Ok(Response::Bytes(rval))
    }
}
