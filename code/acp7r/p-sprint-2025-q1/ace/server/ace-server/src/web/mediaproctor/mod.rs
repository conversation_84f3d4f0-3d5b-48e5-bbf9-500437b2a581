pub mod instance_status;
pub mod process;
pub mod stream;
pub mod terminate_instance;

async fn auth(
    name: &str,
    secret: &str,
    app: &impl crate::App,
) -> granite::Result<ace_graph::mediaproctor::MediaProctor> {
    let mediaproctor_list = match ace_graph::mediaproctor::select(
        ace_graph::MediaProctorFilter::All,
        &app.zero_system().ace_core_app.ace_db_app,
    )
    .await
    {
        Ok(mediaproctor_list) => mediaproctor_list,
        Err(e) => {
            return Err(granite::from_error_stack!(e));
        }
    };

    // find the mediaproctor entry with post.name
    let mediaproctor = match mediaproctor_list.iter().find(|mp| mp.name == name) {
        Some(mediaproctor) => mediaproctor,
        None => {
            return Err(granite::Error::new(granite::ErrorType::DataNotFound)
                .add_context(format!("No mediaproctor entry found with name {}", name)));
        }
    };

    // validate the secret
    if mediaproctor.secret != secret {
        return Err(granite::Error::new(granite::ErrorType::Unexpected)
            .add_context(format!("Invalid secret for name {}", name)));
    }

    Ok(mediaproctor.clone())
}
