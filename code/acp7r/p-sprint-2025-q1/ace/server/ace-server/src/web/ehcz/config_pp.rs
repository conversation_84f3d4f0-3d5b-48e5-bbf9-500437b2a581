#[approck::http(GET /ehcz/config-pp?app_name=String; AUTH None; return JSON|Text;)]
pub mod config {

    #[derive(serde::Serialize)]
    pub struct ConfigResponse {
        pub app_name: String,
        pub config: toml::value::Table,
        pub suffix: String,
        pub region: String,
    }

    enum ProductionOrPreview {
        Production,
        Preview,
    }

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let production_or_preview = match auth_basic.0.as_str() {
            "production" => ProductionOrPreview::Production,
            "preview" => ProductionOrPreview::Preview,
            _ => {
                let response = Text {
                    content: "username must be `production` or `preview`".to_string(),
                    status: approck::StatusCode::UNAUTHORIZED,
                    ..Default::default()
                };
                return Ok(Response::Text(response));
            }
        };

        let secret = auth_basic.1;

        let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

        let config = match ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app).await {
            Ok(config) => config,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let app = match crate::web::ehcz::authenticate_app(qs.app_name.clone(), secret, app).await {
            Ok(app) => app,
            Err(_e) => {
                let response = Text {
                    content: "Invalid credentials".to_string(),
                    status: approck::StatusCode::UNAUTHORIZED,
                    ..Default::default()
                };
                return Ok(Response::Text(response));
            }
        };

        let config_response = match production_or_preview {
            ProductionOrPreview::Production => ConfigResponse {
                app_name: qs.app_name,
                config: app.conf_merged_production.clone(),
                suffix: "".to_string(),
                region: config.region,
            },
            ProductionOrPreview::Preview => {
                let instance_preview_graphkey =
                    ace_graph::Instance::AppPreview(app.graphkey.clone());
                let instance_preview =
                    match ace_graph::ins::get(&instance_preview_graphkey, ace_db_app).await {
                        Ok(instance_preview) => instance_preview,
                        Err(e) => {
                            let response = Text {
                                content: format!("Error loading Preview config: {e:#?}")
                                    .to_string(),
                                status: approck::StatusCode::INTERNAL_SERVER_ERROR,
                                ..Default::default()
                            };
                            return Ok(Response::Text(response));
                        }
                    };

                ConfigResponse {
                    app_name: qs.app_name,
                    config: app.conf_merged_preview.clone(),
                    suffix: format!(".{}", instance_preview.public_hostname),
                    region: config.region,
                }
            }
        };

        let rval = serde_json::to_string(&config_response)?;

        Ok(Response::JSON(rval.into()))
    }
}
