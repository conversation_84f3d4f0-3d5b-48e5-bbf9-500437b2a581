[package]
name = "ace-server-zero"
version = "0.1.0"
edition = "2024"


[package.metadata.acp]
module = {}
extends = ["bux","granite"]

[dependencies]

ace-core = { path = "../../core/ace-core" }
ace-graph = { path = "../../core/ace-graph" }
ace-types = { path = "../../shared/ace-types" }

approck = { workspace = true }
async-trait = { workspace = true }
bux = { workspace = true }
chrono = { workspace = true }
granite = { workspace = true }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
