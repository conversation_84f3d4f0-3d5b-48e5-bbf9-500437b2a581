#[approck::http(GET|POST /ace-ssh/sign; AUTH None; return Bytes|Text;)]
pub mod page {

    #[derive(Debug)]
    struct PostForm {
        fingerprint: String,
        principal: Vec<String>,
        expire: Option<u32>,
        signature: String,
    }

    pub async fn request(app: App, req: Request, post: Option<PostForm>) -> Response {
        use http::Method;

        match req.method() {
            Method::GET => {
                let text = garbage::STSL!(
                    r#"
                    ** You have reached the ace-ssh/sign endpoint **

                    To sign a certificate, send a POST request to this endpoint with the following parameters:
                        POST /ace-ssh/sign
                        content-type: application/x-www-form-urlencoded

                    The contents of the POST must be these fields
                        `fingerprint`
                            obtain with the following command
                            ssh keygen -l -E sha256 -f private_key_path

                        `principal`
                            principals to sign the certificate for, for example
                                user@hostname
                            can send this zero or more times

                        `expire`
                            optional number of seconds to expire the certificate after
                            
                        `signature`
                            the base64 encoded signature of the fingerprint, principal, and expire parameters
                            obtained by building a properly encoded query string:
                                "fingerprint=...&principal=...&expire=..."
                            and piping it through:
                                echo "..." | ssh-keygen -Y sign -f private_key_path
                "#
                );
                return Response::Text(text.into());
            }
            Method::POST => {
                let post = post.expect("method was POST, but why is post None?");

                eprintln!(
                    "Received certificate signing request:\n    fingerprint: {}\n    principals: {:?}\n    expire: {:?}\n",
                    post.fingerprint, post.principal, post.expire
                );

                let db_app = &app.zero_system().ace_core_app.ace_db_app;
                let temp_path = app.zero_system().ace_core_app.temp_path.clone();

                match ace_core::vpn::ssh::get_certificate(
                    db_app,
                    temp_path,
                    post.fingerprint,
                    post.principal,
                    post.expire,
                    post.signature,
                )
                .await
                {
                    Ok(cert) => return Response::Text(cert.into()),
                    Err(e) => {
                        return Response::Text(Text::new_with_status(
                            e,
                            http::StatusCode::UNAUTHORIZED,
                        ));
                    }
                }
            }
            _ => {
                return Response::Text(Text::new_with_status(
                    "Invalid HTTP Method\n",
                    http::StatusCode::METHOD_NOT_ALLOWED,
                ));
            }
        }
    }
}
