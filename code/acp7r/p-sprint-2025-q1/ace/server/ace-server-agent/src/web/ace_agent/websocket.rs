#[approck::http(GET /ace-agent/websocket?gk=String; AUTH None; return Empty|WebSocketUpgrade;)]
pub mod page {
    pub async fn request(app: App, req: Request, qs: QueryString) -> Response {
        let upgrade = req
            .upgrade_to_websocket(move |socket| {
                let gk = qs.gk.clone();
                async move { self::websocket(app, socket, gk).await }
            })
            .await;

        match upgrade {
            Ok(Some(response)) => return Response::WebSocketUpgrade(response),
            Err(error) => eprintln!("Error upgrading connection to websocket: {error:?}"),
            _ => {}
        }

        Response::Empty(Empty::default())
    }

    async fn websocket(
        app: &'static impl crate::App,
        mut websocket: WebSocket,
        host_gk: String,
    ) -> granite::Result<()> {
        let mut individual_listener = match app.register_agent(host_gk.clone()).await {
            Ok(listener) => listener,
            Err(e) => {
                println!("Error registering agent: {e:?}");

                let not_authorized = ace_types::Message_s2a {
                    msg_id: uuid::Uuid::new_v4().to_string(),
                    job: ace_types::Job_s2a::NotAuthorized,
                    timestamp: chrono::Utc::now().timestamp(),
                    msg_type: ace_types::Message_Type::Prompt,
                };

                app.zero_system()
                    .update_bad_conn_list(host_gk.clone(), e.clone())
                    .await;

                // Broadcast client update here...
                let bad_connection = ace_server_zero::BadConnection {
                    gk_string: host_gk,
                    ip_addr: std::net::IpAddr::V4(std::net::Ipv4Addr::new(0, 0, 0, 0)),
                    timestamp: chrono::Utc::now().timestamp(),
                    reason: e,
                };

                let client_update = ace_server_zero::ClientUpdate::BadConnection(bad_connection);
                app.broadcast_to_clients(client_update).await?;

                let msg_serialized = serde_json::to_string(&not_authorized)?;

                websocket.send(msg_serialized.into()).await?;
                websocket.close().await?;
                return Ok(());
            }
        };

        loop {
            tokio::select! {
                // Messages from the agent:
                msg = websocket.recv() => {
                    match msg {
                        Some(Ok(message)) => {
                            match message.into_data() {
                                WebSocketMessageData::Text(text) => {
                                    println!("Received from Agent/Instance {}: {}", &individual_listener.graphkey, text);
                                    let a2s_msg = serde_json::from_str::<ace_types::Message_a2s>(&text)?;

                                    // Deconstruct the message
                                    let msg_id = a2s_msg.msg_id;
                                    let job = a2s_msg.job;
                                    let timestamp = a2s_msg.timestamp;
                                    let _msg_type = a2s_msg.msg_type;

                                    // For purposes of responding, check job:
                                    match &job {
                                        ace_types::Job_a2s::Ping => {
                                            let reply = ace_types::Message_s2a {
                                                msg_id: msg_id.clone(),
                                                job: ace_types::Job_s2a::Pong,
                                                timestamp,
                                                msg_type: ace_types::Message_Type::Reply,
                                            };

                                            let ser_reply = serde_json::to_string(&reply)?;
                                            println!("Sending Pong back to agent");

                                            websocket
                                                .send(ser_reply.into())
                                                .await?;
                                        }
                                        ace_types::Job_a2s::Pong => println!("Received Pong from agent {}", &individual_listener.graphkey),
                                        ace_types::Job_a2s::SystemInfo(_info) => {},
                                    }

                                    app.update_host_status(msg_id, &individual_listener.graphkey, job, timestamp).await?;
                                }
                                WebSocketMessageData::Close => {
                                    app.deregister_agent(&individual_listener.graphkey).await?;
                                    println!("Connection closed");
                                }
                                _ => {}
                            }
                        }
                        Some(Err(e)) => println!("Error receiving message: {e:?}"),
                        None => {
                            app.deregister_agent(&individual_listener.graphkey).await?;
                            break Ok(());
                        }
                    }
                }

                // Messages to send TO the agent:
                msg = individual_listener.listener.recv() => {
                    if let Some(msg) = msg {
                        println!("Heard message on individual listener: {:?}", &individual_listener.graphkey);

                        websocket
                            .send(msg.into())
                            .await?;
                    }
                }
            }
        }
    }
}
