use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetAwsElbTg,
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let tg_result =
        ace_graph::aws_elb_tg::select_result(ace_graph::AwsElbTgFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut tgs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid Target Group entries
    match tg_result {
        Ok(tgs) => {
            for tg in tgs {
                match tg {
                    Ok(tg) => tgs_sorted.push(tg),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AWS ELB Target Groups: {:#?}", e);
            return;
        }
    }

    // Sort Target Groups by their graphkey
    tgs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for tg in tgs_sorted {
            println!("\n{:#?}", tg);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Name", "Purpose", "Protocol", "Port"]);

        for tg in tgs_sorted {
            table.add_row(vec![
                tg.graphkey.to_string(),
                tg.name.clone(),
                tg.purpose.clone(),
                tg.protocol.to_string(),
                tg.port.to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}

pub async fn info(
    gk_aws_elb_tg: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let gk_aws_elb_tg = ace_graph::AwsElbTg::deserialize(gk_aws_elb_tg)
        .map_err(|e| ErrorStack::InvalidGraphKey(gk_aws_elb_tg.to_owned(), e.to_string()))?;

    let tg = ace_graph::aws_elb_tg::get(gk_aws_elb_tg, ace_db_app)
        .await
        .change_context(ErrorStack::GetAwsElbTg)?;

    println!("{:#?}", tg);
    Ok(())
}
