use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let aws_accounts_result =
        ace_graph::aws_account::select_result(&ace_graph::AwsAccountFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut aws_accounts_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid aws_accounts
    match aws_accounts_result {
        Ok(aws_accounts) => {
            for aws_account in aws_accounts {
                match aws_account {
                    Ok(aws_account) => aws_accounts_sorted.push(aws_account),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AwsAccounts: {:#?}", e);
            return;
        }
    }

    // Sort aws_accounts by graphkey
    aws_accounts_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for aws_account in aws_accounts_sorted {
            println!("\n{:#?}", aws_account);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Account Key",
            "Public Domain",
            "Private Domain",
        ]);

        for aws_account in aws_accounts_sorted {
            table.add_row(vec![
                aws_account.graphkey.to_string(),
                aws_account.account_key.to_string(),
                aws_account.public_domain,
                aws_account.private_domain,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
