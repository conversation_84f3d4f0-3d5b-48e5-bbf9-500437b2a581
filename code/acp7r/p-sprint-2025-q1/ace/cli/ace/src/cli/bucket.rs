use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let buckets_result =
        ace_graph::bucket::select_result(ace_graph::BucketFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut buckets_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid buckets
    match buckets_result {
        Ok(buckets) => {
            for bucket in buckets {
                match bucket {
                    Ok(bucket) => buckets_sorted.push(bucket),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching buckets: {:#?}", e);
            return;
        }
    }

    // Sort buckets by their graphkey
    buckets_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket in buckets_sorted {
            println!("\n{:#?}", bucket);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Name", "Suffix", "Developer Access"]);
        for bucket in buckets_sorted {
            table.add_row(vec![
                bucket.graphkey.to_string(),
                bucket.name,
                bucket.suffix,
                bucket.developer_access.to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}

pub async fn info(gk_bucket: &str, ace_db_app: &ace_db::App) {
    // Parse the graphkey from string
    let bucket_key = match ace_graph::Bucket::deserialize(gk_bucket) {
        Ok(key) => key,
        Err(e) => {
            eprintln!("Error parsing bucket graphkey: {}", e);
            return;
        }
    };

    // Get the bucket details
    let bucket_result = ace_graph::bucket::get(bucket_key, ace_db_app).await;

    match bucket_result {
        Ok(bucket) => {
            println!("Bucket Information:");
            println!("  Graphkey:       {}", bucket.graphkey);
            println!("  Name:           {}", bucket.name);
            println!("  Suffix:         {}", bucket.suffix);
            println!("  Developer Access: {}", bucket.developer_access);

            if !bucket.public_read.is_empty() {
                println!("\nPublic Read Paths:");
                for path in bucket.public_read {
                    println!("  - {}", path);
                }
            } else {
                println!("\nPublic Read Paths: None");
            }

            if !bucket.bucket_policy_statements.is_empty() {
                println!("\nBucket Policy Statements:");
                for (i, statement) in bucket.bucket_policy_statements.iter().enumerate() {
                    print!("Statement {}: ", i + 1);
                    println!("{:#?}", statement);
                }
            } else {
                println!("\nBucket Policy Statements: None");
            }

            println!("\nSource: {}", bucket.source);
        }
        Err(e) => {
            eprintln!("Error fetching bucket information: {:#?}", e);
        }
    }
}
