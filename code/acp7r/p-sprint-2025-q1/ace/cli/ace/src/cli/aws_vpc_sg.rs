use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let security_groups_result = ace_graph::aws_vpc_sg::select_result(
        &ace_graph::AwsVpcSecurityGroupFilter::All,
        ace_db_app,
    )
    .await;
    let mut errors = Vec::new();
    let mut security_groups_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid security group entries
    match security_groups_result {
        Ok(security_groups) => {
            for security_group in security_groups {
                match security_group {
                    Ok(security_group) => security_groups_sorted.push(security_group),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching security groups: {:#?}", e);
            return;
        }
    }

    // Sort security groups by their graphkey
    security_groups_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for security_group in security_groups_sorted {
            println!("\n{:#?}", security_group);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Description", "Source"]);
        for security_group in security_groups_sorted {
            table.add_row(vec![
                security_group.graphkey.to_string(),
                security_group.description,
                security_group.source,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
