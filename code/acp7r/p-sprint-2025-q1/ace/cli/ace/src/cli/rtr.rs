use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let route_table_routes_result =
        ace_graph::rtr::select_result(ace_graph::RouteTableRouteFilter::All).await;
    let mut errors = Vec::new();
    let mut route_table_routes_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid route table route entries
    match route_table_routes_result {
        Ok(route_table_routes) => {
            for route_table_route in route_table_routes {
                match route_table_route {
                    Ok(route_table_route) => route_table_routes_sorted.push(route_table_route),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching route table routes: {:#?}", e);
            return;
        }
    }

    // Sort route table routes by their graphkey
    route_table_routes_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for route_table_route in route_table_routes_sorted {
            println!("\n{:#?}", route_table_route);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for route_table_route in route_table_routes_sorted {
            table.add_row(vec![route_table_route.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
