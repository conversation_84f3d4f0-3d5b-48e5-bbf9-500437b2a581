use error_stack::ResultExt;
use serde_json::{Map, Value};
use std::{fs, path::Path};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CreateFile,
    DeserializeValue,
    GlobPatternError,
    OpenFile,
    PackerManifestJsonAndOldManifestsExist,
    Regex,
    RemoveFile,
    ToWriterPretty,
}

pub async fn run(app: &ace_core::Application) {
    // This is the current update mechanism
    let binpath = &app
        .bin_path
        .join("agit-upgrade")
        .to_string_lossy()
        .to_string();

    // Use bash to run the script
    let path = std::ffi::CString::new("/bin/bash").unwrap();
    let script_path = std::ffi::CString::new(binpath.as_bytes()).unwrap();

    // execv replaces the current process with the script.  Otherwise this might be locked and in the way.
    nix::unistd::execv(&path, &[path.clone(), script_path]).unwrap();

    //NOTE: It never makes it to here
}

macro_rules! remove_file {
    ($path:expr) => {
        if std::path::Path::new($path).exists() {
            std::fs::remove_file($path).change_context(ErrorStack::RemoveFile)?;
        }
    };
}

pub async fn fixup(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    remove_file!(&app.path.join("bin/update-from-git-repo"));

    fixup_packer_manifest(app).await?;
    fixup_old_tlscerts(app).await?;
    Ok(())
}

/// This function is used to merge the old packer.manifest.ubuntu*.json files into the new packer.manifest.json file.
/// Conditions for running:
/// 1. The new file does not exist
/// 2. At least one old file exists
///
/// Rationale for only running once
/// 1. The old files are removed after processing
async fn fixup_packer_manifest(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let data_path = app.ace_db_app.data_path.clone();
    let old_filename_pattern = "packer.manifest.ubuntu*.json";
    let old_pattern = format!("{}/{}", data_path.to_string_lossy(), old_filename_pattern);
    let final_manifest_path = format!("{}/packer.manifest.json", data_path.to_string_lossy());

    // Use glob to find files matching the pattern and collect them
    let old_manifest_files: Vec<_> = glob::glob(&old_pattern)
        .change_context(ErrorStack::GlobPatternError)?
        .filter_map(Result::ok)
        .collect();

    // if there are no old files to convert, then exit early
    if old_manifest_files.is_empty() {
        return Ok(());
    }

    // If there are old files, but still anew file, this is bad.
    if !old_manifest_files.is_empty() && Path::new(&final_manifest_path).exists() {
        eprintln!(
            "Error: packer.manifest.json AND the old versions concurrently exist.  This is not allowed.  Please fix this manually."
        );
        error_stack::bail!(ErrorStack::PackerManifestJsonAndOldManifestsExist);
    }

    let mut builds = Vec::new();
    let mut last_run_uuid = String::new();

    for entry in old_manifest_files {
        let file = fs::File::open(entry.clone()).change_context(ErrorStack::OpenFile)?;
        let json: Value =
            serde_json::from_reader(file).change_context(ErrorStack::DeserializeValue)?;
        if let Some(build_array) = json.get("builds").and_then(|v| v.as_array()) {
            for build in build_array {
                builds.push(build.clone());
            }
        }
        if let Some(uuid) = json.get("last_run_uuid").and_then(|v| v.as_str()) {
            last_run_uuid = uuid.to_string();
        }

        // Remove the file after processing
        fs::remove_file(entry).change_context(ErrorStack::RemoveFile)?;
    }

    // Merge JSON data
    let mut merged = Map::new();
    merged.insert("builds".to_string(), Value::Array(builds));
    merged.insert("last_run_uuid".to_string(), Value::String(last_run_uuid));

    // Write merged JSON to new file
    let merged_file =
        fs::File::create(&final_manifest_path).change_context(ErrorStack::CreateFile)?;
    serde_json::to_writer_pretty(merged_file, &merged)
        .change_context(ErrorStack::ToWriterPretty)?;

    // autocommit
    ace_core::git::autocommit(
        app,
        &"auto commit: merged packer manifest files and removed old versions".to_string(),
    )
    .await
    .change_context(ErrorStack::AutoCommit)?;

    Ok(())
}

async fn fixup_old_tlscerts(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let data_path = app.ace_db_app.data_path.clone();

    let old_filepaths = [
        data_path.join("ace-server.crt.pem"),
        data_path.join("ace-server.key.pem"),
        data_path.join("tls.ace-server.cert.pem"),
        data_path.join("tls.ace-server.key.pem"),
    ];

    // If any of them exist, delete them
    for old_path in old_filepaths.iter() {
        if old_path.exists() {
            tokio::fs::remove_file(old_path)
                .await
                .change_context(ErrorStack::RemoveFile)?;
        }
    }

    Ok(())
}
