use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let sgr_result =
        match ace_graph::aws_vpc_sgr::select_result(ace_graph::AwsVpcSecurityGroupRuleFilter::All)
            .await
        {
            Ok(sgr) => sgr,
            Err(e) => {
                eprintln!("Error: {:#?}", e);
                return;
            }
        };

    let mut errors = Vec::new();
    let mut sgr_sorted = Vec::new();

    for sgr in sgr_result {
        match sgr {
            Ok(sgr) => sgr_sorted.push(sgr),
            Err(e) => errors.push(e),
        }
    }

    // Sort security group rules by their graphkey
    sgr_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for sgr in sgr_sorted {
            println!("\n{:#?}", sgr);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);

        for sgr in sgr_sorted {
            table.add_row(vec![sgr.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
