use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let subnet_group_result =
        ace_graph::aws_rds_sng::select_result(ace_graph::AwsRdsSngFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut subnet_groups_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid subnet group entries
    match subnet_group_result {
        Ok(subnet_groups) => {
            for subnet_group in subnet_groups {
                match subnet_group {
                    Ok(subnet_group) => subnet_groups_sorted.push(subnet_group),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AWS RDS Subnet Groups: {:#?}", e);
            return;
        }
    }

    // Sort subnet groups by their graphkey
    subnet_groups_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for subnet_group in subnet_groups_sorted {
            println!("\n{:#?}", subnet_group);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Name", "Description"]);

        for subnet_group in subnet_groups_sorted {
            let description = subnet_group
                .description
                .unwrap_or_else(|| "N/A".to_string());

            table.add_row(vec![
                subnet_group.graphkey.to_string(),
                subnet_group.name.clone(),
                description,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}

pub async fn info(gk_aws_rds_subnet_group: &str, ace_db_app: &ace_db::App) {
    // parse the graphkey from string
    let subnet_group_key = ace_graph::AwsRdsSng::deserialize(gk_aws_rds_subnet_group)
        .unwrap_or_else(|e| {
            panic!("Error parsing subnet group graphkey: {}", e);
        });

    let subnet_group = ace_graph::aws_rds_sng::get(subnet_group_key, ace_db_app)
        .await
        .unwrap_or_else(|e| {
            panic!("Error fetching subnet group: {:#?}", e);
        });

    println!("{:#?}", subnet_group);
}
