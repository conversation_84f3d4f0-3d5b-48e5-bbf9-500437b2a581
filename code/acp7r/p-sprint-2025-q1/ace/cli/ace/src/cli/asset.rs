use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let assets_result =
        ace_graph::asset::select_result(&ace_graph::AssetFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut assets_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid asset entries
    match assets_result {
        Ok(assets) => {
            for asset in assets {
                match asset {
                    Ok(asset) => assets_sorted.push(asset),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching assets: {:#?}", e);
            return;
        }
    }

    // Sort asset entries by their graphkey
    assets_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for asset in assets_sorted {
            println!("{:#?}", asset);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Name", "Version", "Target", "Hash"]);
        for asset in assets_sorted {
            table.add_row(vec![
                asset.graphkey.to_string(),
                asset.name,
                asset.version,
                asset.target,
                asset.hash,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
