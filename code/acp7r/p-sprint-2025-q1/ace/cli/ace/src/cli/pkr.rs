use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CheckPackerState,
    CheckSecureMountState,
    ExecuteCommand(std::path::PathBuf),
    PackerStateIsUncommitted,
    SecureIsNotMounted,
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let result = ace_graph::pkr::select_result(ace_graph::PackerFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut packers_sorted = Vec::new();

    match result {
        Ok(packers) => {
            for packer in packers {
                match packer {
                    Ok(packer) => packers_sorted.push(packer),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching packers: {:#?}", e);
            return;
        }
    }

    // Sort packers by their graphkey for consistent output
    packers_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for packer in packers_sorted {
            println!("\n{:#?}", packer);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Instance Type", "SSH Username"]);

        for packer in packers_sorted {
            table.add_row(vec![
                packer.graphkey.to_string(),
                packer.instance_type.to_string(),
                packer.ssh_username.to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}

pub async fn run(
    app: &ace_core::Application,
    args: &[String],
) -> error_stack::Result<(), ErrorStack> {
    let mut autocommit = false;

    if let Some(first_arg) = args.first() {
        if first_arg.as_str() == "build" {
            if !app
                .is_secure_mounted()
                .change_context(ErrorStack::CheckSecureMountState)?
            {
                tracing::error!(
                    "This packer command requires secure to be mounted.  Please run `ace secure mount`."
                );
                error_stack::bail!(ErrorStack::SecureIsNotMounted);
            }

            match app.is_dirty("data/packer.*").await {
                Ok(true) => {
                    error_stack::bail!(ErrorStack::PackerStateIsUncommitted);
                }
                Ok(false) => {}
                Err(e) => {
                    return Err(e.change_context(ErrorStack::CheckPackerState));
                }
            }
            autocommit = true;
        }
    }

    let mut cmd = std::process::Command::new(&app.packer_bin_path);
    cmd.args(args.iter());
    cmd.current_dir(&app.packer_path);
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    let _output = cmd
        .output()
        .change_context(ErrorStack::ExecuteCommand(app.packer_bin_path.clone()))?;

    if autocommit {
        ace_core::git::autocommit(app, &"auto commit after packer operation".to_string())
            .await
            .change_context(ErrorStack::AutoCommit)?;
    }

    Ok(())
}
