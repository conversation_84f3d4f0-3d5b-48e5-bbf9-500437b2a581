use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let brdsts_result =
        ace_graph::brdst::select_result(&ace_graph::BrdstFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut brdsts_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid brdsts
    match brdsts_result {
        Ok(brdsts) => {
            for brdst in brdsts {
                match brdst {
                    Ok(brdst) => brdsts_sorted.push(brdst),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching brdsts: {:#?}", e);
            return;
        }
    }

    // Sort brdsts by their graphkey
    brdsts_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for brdst in brdsts_sorted {
            println!("\n{:#?}", brdst);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Source Bucket Arn",
            "Source AccountKey@Region",
            "Target Bucket Arn",
        ]);

        for brdst in brdsts_sorted {
            let source_account_key_region =
                format!("{}@{}", brdst.source.account_key, brdst.source.region);
            table.add_row(vec![
                brdst.graphkey.to_string(),
                brdst.source.bucket_arn,
                source_account_key_region,
                brdst.target.bucket_arn,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
