use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let route_tables_result = ace_graph::rt::select_result(ace_graph::RouteTableFilter::All).await;
    let mut errors = Vec::new();
    let mut route_tables_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid route table entries
    match route_tables_result {
        Ok(route_tables) => {
            for route_table in route_tables {
                match route_table {
                    Ok(route_table) => route_tables_sorted.push(route_table),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching route tables: {:#?}", e);
            return;
        }
    }

    // Sort route tables by their graphkey
    route_tables_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for route_table in route_tables_sorted {
            println!("\n{:#?}", route_table);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for route_table in route_tables_sorted {
            table.add_row(vec![route_table.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
