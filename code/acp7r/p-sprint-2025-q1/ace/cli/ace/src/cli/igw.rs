use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let igws_result = ace_graph::igw::select_result(ace_graph::IgwFilter::All).await;
    let mut errors = Vec::new();
    let mut igws_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IGW entries
    match igws_result {
        Ok(igws) => {
            for igw in igws {
                match igw {
                    Ok(igw) => igws_sorted.push(igw),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching IGWs: {:#?}", e);
            return;
        }
    }

    // Sort IGWs by their graphkey
    igws_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for igw in igws_sorted {
            println!("\n{:#?}", igw);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for igw in igws_sorted {
            table.add_row(vec![igw.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
