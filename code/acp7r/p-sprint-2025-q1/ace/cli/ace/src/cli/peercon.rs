use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let result =
        ace_graph::peercon::select_result(&ace_graph::PeerconFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut peercons_sorted = Vec::new();

    match result {
        Ok(peercons) => {
            for peercon in peercons {
                match peercon {
                    Ok(peercon) => peercons_sorted.push(peercon),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching peer connections: {:#?}", e);
            return;
        }
    }

    // Sort peer connections by their graphkey
    peercons_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for peercon in peercons_sorted {
            println!("\n{:#?}", peercon);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Peercon ID"]);

        for peercon in peercons_sorted {
            table.add_row(vec![
                peercon.graphkey.to_string(),
                peercon.peering_connection_id.to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
