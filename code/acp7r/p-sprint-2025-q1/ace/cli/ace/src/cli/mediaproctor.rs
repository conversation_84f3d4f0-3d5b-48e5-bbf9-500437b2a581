use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CheckInstanceStatus,
    Deserialize,
    GetMediaproctor(String),
    ReadFile,
    RunMpProcess,
    RunMpStream,
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let mediaproctors_result =
        ace_graph::mediaproctor::select_result(&ace_graph::MediaProctorFilter::All, ace_db_app)
            .await;
    let mut errors = Vec::new();
    let mut mediaproctors_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid mediaproctor entries
    match mediaproctors_result {
        Ok(mediaproctors) => {
            for mediaproctor in mediaproctors {
                match mediaproctor {
                    Ok(mediaproctor) => mediaproctors_sorted.push(mediaproctor),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching mediaproctors: {:#?}", e);
            return;
        }
    }

    // Sort mediaproctors by their graphkey
    mediaproctors_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for mediaproctor in mediaproctors_sorted {
            println!("\n{:#?}", mediaproctor);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "AMI Graphkey",
            "Secret",
            "Security Group ID",
        ]);

        for mediaproctor in mediaproctors_sorted {
            let security_group_id = mediaproctor
                .security_group_id
                .as_ref()
                .map_or("None".to_string(), ToString::to_string);
            table.add_row(vec![
                mediaproctor.graphkey.to_string(),
                mediaproctor.ami_graphkey.to_string(),
                mediaproctor.secret.to_string(),
                security_group_id,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
pub async fn mp_process(
    mp_name: &str,
    jobpath: &Path,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Read the file content asynchronously
    let file_content = tokio::fs::read_to_string(jobpath)
        .await
        .change_context(ErrorStack::ReadFile)?;

    // Deserialize the JSON content into serde_json::Value
    let job: serde_json::Value =
        serde_json::from_str(&file_content).change_context(ErrorStack::Deserialize)?;

    let mediaproctor =
        ace_graph::mediaproctor::get(&ace_graph::MediaProctor::Db(mp_name.to_owned()), ace_db_app)
            .await
            .change_context(ErrorStack::GetMediaproctor(mp_name.to_string()))?;

    let mut tags = HashMap::new();
    tags.insert("Origin".to_string(), "ace-cli".to_string());

    // Your logic here to work with json_value
    let rval = ace_core::mediaproctor::run_mp_process(&mediaproctor, &job, &tags, ace_db_app)
        .await
        .change_context(ErrorStack::RunMpProcess)?;

    println!("{:?}", rval);

    Ok(())
}

pub async fn mp_stream(
    mp_name: &str,
    job_url: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mediaproctor =
        ace_graph::mediaproctor::get(&ace_graph::MediaProctor::Db(mp_name.to_owned()), ace_db_app)
            .await
            .change_context(ErrorStack::GetMediaproctor(mp_name.to_string()))?;

    let mut tags = HashMap::new();
    tags.insert("Origin".to_string(), "ace-cli".to_string());

    // Your logic here to work with json_value
    let rval = ace_core::mediaproctor::run_mp_stream(&mediaproctor, job_url, &tags, ace_db_app)
        .await
        .change_context(ErrorStack::RunMpStream)?;

    println!("{:?}", rval);

    Ok(())
}

pub async fn status(
    mp_name: &str,
    instance_ids: &Vec<String>,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Load mediaproctor config
    let mediaproctor =
        ace_graph::mediaproctor::get(&ace_graph::MediaProctor::Db(mp_name.to_owned()), ace_db_app)
            .await
            .change_context(ErrorStack::GetMediaproctor(mp_name.to_string()))?;

    // Your logic here to work with json_value
    let rval = ace_core::mediaproctor::check_instance_status(&mediaproctor, instance_ids)
        .await
        .change_context(ErrorStack::CheckInstanceStatus)?;

    println!("{:?}", rval);

    Ok(())
}
