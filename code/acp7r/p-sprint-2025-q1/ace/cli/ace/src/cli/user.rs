use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetUser,
}

pub async fn info(gk_user: &str, ace_db_app: &ace_db::App) -> error_stack::Result<(), ErrorStack> {
    let gk_user = if let Ok(graphkey) = ace_graph::User::deserialize(gk_user) {
        graphkey
    } else {
        let filter = ace_graph::UserFilter::All;
        let users = ace_graph::user::select(&filter, ace_db_app)
            .await
            .change_context(ErrorStack::GetUser)?;
        users
            .into_iter()
            .find(|u| u.name == gk_user)
            .map(|u| u.graphkey)
            .ok_or_else(|| {
                ErrorStack::InvalidGraphKey(gk_user.to_owned(), "Username not found".to_string())
            })?
    };

    let user = ace_graph::user::get(&gk_user, ace_db_app)
        .await
        .change_context(ErrorStack::GetUser)?;

    println!("User Summary:");
    println!("  Graph Key:            {}", user.graphkey.serialize());
    println!("  Name:                 {}", user.name);
    println!("\nSSH Keys:");
    for key in &user.ssh_keys {
        println!("  - {}", key);
    }

    println!("\nStatic Networks:");
    for network in &user.static_networks {
        println!("  - {}", network);
    }

    println!("\nAllowed Principals:");
    for principal in &user.allowed_principals {
        println!("  - {}", principal);
    }

    Ok(())
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let users = match ace_graph::user::select_result(&ace_graph::UserFilter::All, ace_db_app).await
    {
        Ok(users) => users,
        Err(e) => {
            eprintln!("Error: {:#?}", e);
            return;
        }
    };

    let mut errors = Vec::new();
    let mut users_sorted = Vec::new();

    for user in users {
        match user {
            Ok(user) => users_sorted.push(user),
            Err(e) => errors.push(e),
        }
    }

    // Sort users by their graphkey
    users_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for user in users_sorted {
            println!("\n{:#?}", user);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Username", "Graphkey", "Allowed Principals"]);

        for user in users_sorted {
            let allowed_principals = user
                .allowed_principals
                .iter()
                .map(|x| x.to_string())
                .collect::<Vec<String>>()
                .join(", ");

            table.add_row(vec![
                user.name,
                user.graphkey.to_string(),
                allowed_principals,
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
