use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DockerBuildCommandFailed(std::process::ExitStatus, String),
    DockerPushCommandFailed(std::process::ExitStatus, String),
    DeserializeGraphkey(String, String),
    GetDocker,
    GetTagName,
    ExecuteDockerBuild,
    ExecuteDockerPush,
}

pub async fn build_image(
    gk_docker: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Deserialize graphkey
    let graphkey = match ace_graph::Docker::deserialize(gk_docker) {
        Ok(graphkey) => graphkey,
        Err(e) => {
            error_stack::bail!(ErrorStack::DeserializeGraphkey(gk_docker.to_owned(), e));
        }
    };

    // Get the docker
    let docker = ace_graph::docker::get(&graphkey, ace_db_app)
        .await
        .change_context(ErrorStack::GetDocker)?;

    let directory = &ace_db_app.docker_path.join(docker.name.clone());
    let tag_name = docker
        .get_tag_name(ace_db_app)
        .await
        .change_context(ErrorStack::GetTagName)?;

    // Build the image
    let mut cmd = std::process::Command::new("docker");
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.current_dir(directory);
    cmd.arg("build");
    cmd.arg("--tag").arg(tag_name);
    cmd.arg(".");

    let output = cmd
        .output()
        .change_context(ErrorStack::ExecuteDockerBuild)?;

    // fail if command failed
    if !output.status.success() {
        error_stack::bail!(ErrorStack::DockerBuildCommandFailed(
            output.status,
            String::from_utf8_lossy(&output.stderr).to_string()
        ));
    }

    Ok(())
}

pub async fn info(gk_docker: &str, ace_db_app: &ace_db::App) {
    let graphkey = match ace_graph::Docker::deserialize(gk_docker) {
        Ok(graphkey) => graphkey,
        Err(e) => {
            eprintln!("{:#?}", e);
            return;
        }
    };
    let docker = ace_graph::docker::get(&graphkey, ace_db_app).await;

    match docker {
        Ok(d) => {
            println!("Docker: {}", d.graphkey);
            println!("Name: {}", d.name);
            println!("Path: {}", d.path.display());

            match d.get_tag_name(ace_db_app).await {
                Ok(tag_name) => {
                    println!("Tag Name: {}", tag_name);
                }
                Err(e) => {
                    println!("Error getting tag name:\n{:#?}", e);
                }
            }
        }
        Err(e) => {
            eprintln!("{:#?}", e);
        }
    }
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let dockers_result =
        ace_graph::docker::select_result(&ace_graph::DockerFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut dockers_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid Docker entries
    match dockers_result {
        Ok(dockers) => {
            for docker in dockers {
                match docker {
                    Ok(docker) => dockers_sorted.push(docker),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching dockers: {:#?}", e);
            return;
        }
    }

    // Sort Docker entries by their graphkey
    dockers_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for docker in dockers_sorted {
            println!("{:#?}\n", docker);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Path"]);
        for docker in dockers_sorted {
            table.add_row(vec![
                docker.graphkey.to_string(),
                docker.path.display().to_string(),
            ]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            eprintln!("{:#?}", error);
        }
    }
}
pub async fn push_image(
    gk_docker: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let graphkey = match ace_graph::Docker::deserialize(gk_docker) {
        Ok(graphkey) => graphkey,
        Err(e) => {
            error_stack::bail!(ErrorStack::DeserializeGraphkey(gk_docker.to_owned(), e));
        }
    };

    let docker = ace_graph::docker::get(&graphkey, ace_db_app)
        .await
        .change_context(ErrorStack::GetDocker)?;
    let tag_name = docker
        .get_tag_name(ace_db_app)
        .await
        .change_context(ErrorStack::GetTagName)?;

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new("docker");
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.arg("push");
    cmd.arg(tag_name);

    let output = cmd.output().change_context(ErrorStack::ExecuteDockerPush)?;

    // fail if command failed
    if !output.status.success() {
        error_stack::bail!(ErrorStack::DockerPushCommandFailed(
            output.status,
            String::from_utf8_lossy(&output.stderr).to_string()
        ));
    }

    Ok(())
}
