use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let ins_profiles_result =
        ace_graph::ins_profile::select_result(&ace_graph::InstanceProfileFilter::All, ace_db_app)
            .await;
    let mut errors = Vec::new();
    let mut profiles_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid instance entries
    match ins_profiles_result {
        Ok(ins_profiles) => {
            for ins_profile in ins_profiles {
                match ins_profile {
                    Ok(profile) => profiles_sorted.push(profile),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching instance profiles: {:#?}", e);
            return;
        }
    }

    // Sort instances by their graphkey
    profiles_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for profile in profiles_sorted {
            println!("\n{:#?}", profile);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Source"]);
        for ins_profile in profiles_sorted {
            let row = vec![
                ins_profile.graphkey.to_string(),
                ins_profile.source.to_string(),
            ];
            table.add_row(row);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
