use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let iam_access_keys_result =
        ace_graph::iam_access_key::select_result(ace_graph::IamAccessKeyFilter::All).await;
    let mut errors = Vec::new();
    let mut iam_access_keys_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM access key entries
    match iam_access_keys_result {
        Ok(iam_access_keys) => {
            for iam_access_key in iam_access_keys {
                match iam_access_key {
                    Ok(iam_access_key) => iam_access_keys_sorted.push(iam_access_key),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching IAM access keys: {:#?}", e);
            return;
        }
    }

    // Sort IAM access keys by their graphkey
    iam_access_keys_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_access_key in iam_access_keys_sorted {
            println!("\n{:#?}", iam_access_key);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for iam_access_key in iam_access_keys_sorted {
            table.add_row(vec![iam_access_key.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
