use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let nat_gateway_result =
        ace_graph::nat_gateway::select_result(ace_graph::NatGatewayFilter::All).await;
    let mut errors = Vec::new();
    let mut nat_gateways_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid NAT gateway entries
    match nat_gateway_result {
        Ok(nat_gateways) => {
            for nat_gateway in nat_gateways {
                match nat_gateway {
                    Ok(nat_gateway) => nat_gateways_sorted.push(nat_gateway),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching NAT gateways: {:#?}", e);
            return;
        }
    }

    // Sort NAT gateways by their graphkey
    nat_gateways_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for nat_gateway in nat_gateways_sorted {
            println!("\n{:#?}", nat_gateway);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for nat_gateway in nat_gateways_sorted {
            table.add_row(vec![nat_gateway.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {:#?}", error);
        }
    }
}
