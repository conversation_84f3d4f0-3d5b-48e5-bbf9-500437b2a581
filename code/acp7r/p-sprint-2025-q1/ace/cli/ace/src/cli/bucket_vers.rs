use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let bucket_versions_result =
        ace_graph::bucket_vers::select_result(ace_graph::BucketVersioningFilter::All).await;

    let mut errors = Vec::new();
    let mut bucket_versions_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid items
    match bucket_versions_result {
        Ok(bucket_versions) => {
            for bucket_version in bucket_versions {
                match bucket_version {
                    Ok(bucket_version) => bucket_versions_sorted.push(bucket_version),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching bucket versions: {:#?}", e);
            return;
        }
    }

    // Sort bucket versions by their graphkey
    bucket_versions_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket_version in bucket_versions_sorted {
            println!("\n{:#?}", bucket_version);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for bucket_version in bucket_versions_sorted {
            table.add_row(vec![bucket_version.graphkey.to_string()]);
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }
}
