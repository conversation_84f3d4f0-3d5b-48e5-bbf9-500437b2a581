use ace_graph::GraphKeyExt;
use ace_graph::GraphKeyName;
use ace_graph::app::AppHosting;
use comfy_table::{ContentArrangement, Table, presets};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetApp,
}

pub async fn info(gk_app: &str, ace_db_app: &ace_db::App) -> Result<(), ErrorStack> {
    let gk_app = ace_graph::App::deserialize(gk_app)
        .map_err(|e| ErrorStack::InvalidGraphKey(gk_app.to_owned(), e.to_string()))?;

    let app = ace_graph::app::get(gk_app.get_name().as_str(), ace_db_app)
        .await
        .map_err(|_e| ErrorStack::GetApp)?;

    println!("App Summary: {app:#?}");
    Ok(())
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let apps_result = ace_graph::app::select_result(&ace_graph::AppFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut apps_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid apps
    match apps_result {
        Ok(apps) => {
            for app in apps {
                match app {
                    Ok(app) => apps_sorted.push(app),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching apps: {:#?}", e);
            return;
        }
    }

    // Sort apps by their graphkey
    apps_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for app in apps_sorted {
            println!("\n{:#?}", app);
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Ami",
            "Subnet",
            "Instance Type",
            "Ingress HTTP Public",
            "Use Elastic IP",
            "Volume Size",
        ]);
        for app in apps_sorted {
            match app.hosting {
                Some(AppHosting {
                    ami,
                    subnet,
                    instance_type,
                    ingress_http_public,
                    ingress: _,
                    use_elastic_ip,
                    volume_size,
                }) => {
                    let use_elastic_ip_str = if use_elastic_ip { "Yes" } else { "No" };

                    table.add_row(vec![
                        app.graphkey.to_string(),
                        ami.to_string(),
                        subnet.serialize(),
                        instance_type,
                        ingress_http_public.to_string(),
                        use_elastic_ip_str.to_string(),
                        volume_size.to_string(),
                    ]);
                }
                None => {
                    table.add_row(vec![&app.graphkey.to_string(), "N/A", "N/A", "N/A", "N/A"]);
                }
            }
        }

        println!("{}", table);
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{:#?}", error);
        }
    }

    println!("\nFor AppBucket or Ingress information, run `ace app info <graphkey>`");
}
