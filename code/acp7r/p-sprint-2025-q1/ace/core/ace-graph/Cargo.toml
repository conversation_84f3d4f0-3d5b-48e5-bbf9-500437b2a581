[package]
name = "ace-graph"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ace-db = { path = "../../core/ace-db" }
ace-proc = { path = "../../core/ace-proc" }
garbage = { path = "../../shared/garbage"}
granite = { workspace = true }

chrono = { workspace = true }
error-stack = { workspace = true }
git2 = { workspace = true }
glob = { workspace = true }
hostname = { workspace = true }
indexmap = { workspace = true }
ipnetwork = { workspace = true }
openssl = { workspace = true }
regex.workspace = true
serde = { workspace = true }
serde_json = { workspace = true }
serde_toml = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
ureq = { workspace = true }
