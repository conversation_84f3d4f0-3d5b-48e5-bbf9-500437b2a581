use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConstructFromEtc,
    NotOneHostFound(crate::Host, usize),
    SelectFromDb,
    SelectFromInstances,
}

#[derive(Debug)]
pub struct Host {
    pub graphkey: crate::Host,
    pub hostname: Option<String>,
    pub source: String,
}

impl crate::GraphValueExt for Host {}

pub async fn get(
    gk_host: &crate::Host,
    app: &ace_db::App,
) -> error_stack::Result<Host, ErrorStack> {
    let filter = crate::HostFilter::One(gk_host.clone());

    let mut buckets = select(filter, app).await?;
    if buckets.len() == 1 {
        return Ok(buckets.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneHostFound(gk_host.clone(), buckets.len()));
}

pub async fn select(
    filter: crate::HostFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Host>, ErrorStack> {
    let mut rval = Vec::new();

    for bucket in select_result(&filter, app).await? {
        match bucket {
            Ok(bucket) => rval.push(bucket),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

/// When do we actually need to read any information from disk?
/// 1. When we need to look for etc/host.*.toml files
///
/// Anything else we can just talk to ace_graph::<GraphType> directly for a list of graphkeys.
/// This function will need refactored after Instance filtering is fixed.
pub async fn select_result(
    filter: &crate::HostFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Host, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let (db_filter, ins_filter) = match filter {
        crate::HostFilter::One(gk) => match gk {
            crate::Host::Db(name) => (
                ace_db::Filter::One(name.to_owned()),
                crate::InstanceFilter::None,
            ),
            crate::Host::Instance(ins_gk) => (
                ace_db::Filter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
            ),
            // crate::Host::Workstation(name) => ,
        },
        crate::HostFilter::All => (ace_db::Filter::All, crate::InstanceFilter::All),
        crate::HostFilter::None => (ace_db::Filter::None, crate::InstanceFilter::None),
    };

    let etc_hosts = ace_db::host::select_result(db_filter, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::SelectFromDb)?;

    let instances = crate::ins::select(&ins_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectFromInstances)?;

    for etc_host in etc_hosts {
        match etc_host {
            Ok(etc_host) => {
                // Construct a Host::Db with hostname from the etc_host -ZA
                let host = Host {
                    graphkey: crate::Host::Db(etc_host.name.clone()),
                    hostname: etc_host.hostname.clone(), // Use hostname from etc_host -ZA
                    source: etc_host.source.to_string_lossy().to_string(),
                };
                rval.push(Ok(host));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectFromDb)));
            }
        }
    }

    for instance in instances {
        rval.push(Ok(Host {
            graphkey: crate::Host::Instance(instance.graphkey.clone()),
            hostname: Some(instance.name), //access name field of instance struct- ZA
            source: instance.graphkey.to_string(),
        }));
    }

    Ok(rval)
}
