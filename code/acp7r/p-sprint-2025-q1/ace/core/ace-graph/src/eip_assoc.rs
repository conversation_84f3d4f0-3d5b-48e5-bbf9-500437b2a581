#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneEipFound(crate::EipAssoc, usize),
}

#[derive(Debug)]
pub struct EipAssoc {
    pub graphkey: crate::<PERSON><PERSON>Asso<PERSON>,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for EipAssoc {}

pub async fn get(gk_eipassoc: crate::EipAssoc) -> error_stack::Result<EipAssoc, ErrorStack> {
    let filter = crate::EipAssocFilter::One(gk_eipassoc.clone());

    let mut eip_assocs = select(filter).await?;
    if eip_assocs.len() == 1 {
        return Ok(eip_assocs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneEipFound(gk_eipassoc, eip_assocs.len()));
}

pub async fn select(
    filter: crate::EipAssocFilter,
) -> error_stack::Result<Vec<EipAssoc>, ErrorStack> {
    let mut rval = Vec::new();

    for eip_assocs in select_result(filter).await? {
        match eip_assocs {
            Ok(eip_a) => rval.push(eip_a),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::EipAssocFilter,
) -> error_stack::Result<Vec<error_stack::Result<EipAssoc, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
