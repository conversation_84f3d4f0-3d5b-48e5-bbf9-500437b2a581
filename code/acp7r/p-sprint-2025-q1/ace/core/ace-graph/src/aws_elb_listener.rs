use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsElbListenerFound(crate::AwsElbListener, usize),
    SelectFromAwsElb,
}

#[derive(Debug, Clone)]
pub struct AwsElbListener {
    pub graphkey: crate::AwsElbListener,
    pub aws_elb: crate::AwsElb,
    pub aws_elb_tg: crate::AwsElbTg,
    pub name: String,
    pub protocol: String,
    pub port: u16,
    pub source: String,
}

impl crate::GraphValueExt for AwsElbListener {}

pub async fn get(
    gk_aws_elb_listener: crate::AwsElbListener,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsElbListener, ErrorStack> {
    let filter = crate::AwsElbListenerFilter::One(gk_aws_elb_listener.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsElbListenerFound(
        gk_aws_elb_listener,
        items.len()
    ));
}

pub async fn select(
    filter: crate::AwsElbListenerFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsElbListener>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_elb_listener) => rval.push(aws_elb_listener),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsElbListenerFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsElbListener, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::app::select_result_aws_elb_listener(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromAwsElb)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromAwsElb)),
    );

    Ok(rval)
}
