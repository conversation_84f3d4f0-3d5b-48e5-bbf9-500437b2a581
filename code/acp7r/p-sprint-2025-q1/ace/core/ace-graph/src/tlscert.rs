use std::fmt::Debug;

use crate::GraphKeyExt;
use chrono::TimeZone;
use error_stack::ResultExt;
use openssl::x509::{self};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertAsn1TimeToChrono,
    Get(crate::TlsCert),
    GetConfig,
    NotOneFound(crate::TlsCert, usize),
    ParseAceKeyToCert,
    ReadAceTlsCertFile,
    SelectFromApp,
    SelectFromDeveloper,
}

#[derive(Debug)]
pub struct TlsCert {
    pub graphkey: crate::TlsCert,
    pub common_name: String,
    pub canonical_subject_alt_names: Vec<String>,
    pub cert: Option<TlsCertInner>,
}

#[derive(Debug)]
pub struct TlsCertInner {
    _x509: x509::X509,
    pub subject_alternative_names: Vec<String>,
    pub common_name: String,
    pub expiration: chrono::DateTime<chrono::Utc>,
    pub days_til_expiry: i64,
}

impl TlsCert {
    pub async fn new(
        gk_tlscert: &crate::TlsCert,
        common_name: &str,
        alt_names: Vec<String>,
        ace_db_app: &ace_db::App,
    ) -> error_stack::Result<TlsCert, ErrorStack> {
        let cert_inner = TlsCert::read_self(gk_tlscert, ace_db_app).await?;

        Ok(TlsCert {
            graphkey: gk_tlscert.to_owned(),
            common_name: common_name.to_owned(),
            canonical_subject_alt_names: alt_names,
            cert: cert_inner,
        })
    }

    async fn read_self(
        gk_tlscert: &crate::TlsCert,
        ace_db_app: &ace_db::App,
    ) -> error_stack::Result<Option<TlsCertInner>, ErrorStack> {
        let self_path = ace_db_app
            .data_path
            .join(format!("tls.{}.cert.pem", gk_tlscert.serialize_dashed()));
        if !self_path.exists() {
            return Ok(None);
        }

        let cert_file_contents = tokio::fs::read(&self_path)
            .await
            .change_context(ErrorStack::ReadAceTlsCertFile)?;
        let cert = openssl::x509::X509::from_pem(&cert_file_contents)
            .change_context(ErrorStack::ParseAceKeyToCert)?;

        let subject_alternative_names = {
            let mut names = vec![];
            if let Some(stack) = cert.subject_alt_names() {
                for name in stack.iter() {
                    match (
                        name.directory_name(),
                        name.dnsname(),
                        name.ipaddress(),
                        name.email(),
                    ) {
                        (Some(name), _, _, _) => {
                            let n = format!("{:?}", name);
                            names.push(n);
                        }
                        (_, Some(name), _, _) => {
                            names.push(name.to_string());
                        }
                        (_, _, Some(name), _) => {
                            names.push(format!("IP: {:#?}", name));
                        }
                        (_, _, _, Some(name)) => {
                            names.push(name.to_string());
                        }
                        _ => names.push("Something went wrong".to_string()),
                    }
                }
            }

            names
        };

        let expiration = parse_expire(&cert.not_after().to_string()).await?;
        let days_til_expiry = days_til_expiry(expiration).await;
        let common_name = {
            let thing = cert
                .subject_name()
                .entries_by_nid(openssl::nid::Nid::COMMONNAME)
                .next();
            match thing {
                Some(name) => name.data().as_utf8().unwrap().to_string(),
                None => "No Common Name".to_string(),
            }
        };

        let cert_inner = TlsCertInner {
            _x509: cert,
            subject_alternative_names,
            expiration,
            days_til_expiry,
            common_name,
        };

        Ok(Some(cert_inner))
    }
}

impl crate::GraphValueExt for TlsCert {}

pub async fn get(
    gk_tlscert: &crate::TlsCert,
    app: &ace_db::App,
) -> error_stack::Result<TlsCert, ErrorStack> {
    let filter = crate::TlsCertFilter::One(gk_tlscert.to_owned());
    let mut tlscerts = select_result(&filter, app).await?;

    // Validate that one record was returned
    let tlscert: Result<TlsCert, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if tlscerts.len() == 1 {
            tlscerts.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_tlscert.clone(), tlscerts.len()))
        }
    };

    tlscert.change_context_lazy(|| ErrorStack::Get(gk_tlscert.to_owned()))
}

pub async fn select(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<TlsCert>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for row in select_result(filter, app).await? {
        match row {
            Ok(developer) => {
                rval.push(developer);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<TlsCert, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let ace2_server_name = config.ace2_server_name;

    if matches!(
        filter,
        crate::TlsCertFilter::Ace
            | crate::TlsCertFilter::All
            | crate::TlsCertFilter::One(crate::TlsCert::Ace)
    ) {
        let ace_gk = crate::TlsCert::Ace;
        let canonical_alt_names = vec![ace2_server_name.clone()];

        // **IMPORTANT** use TlsCert::new() to have it automatically read itself from the filesystem (if it exists)
        let ace_tlscert = TlsCert::new(&ace_gk, &ace2_server_name, canonical_alt_names, app).await;
        rval.push(ace_tlscert);
    }

    // Call every place that tls certs can be found
    rval.extend(
        crate::developer::select_result_tlscert(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDeveloper)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDeveloper)),
    );
    rval.extend(
        crate::app::select_result_tlscert(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

/// Parses an x509 certificate and returns the expiration time converted from ASN1TIME to chrono::DateTime<Utc>.
async fn parse_expire(
    expire_asn1time_str: &str,
) -> error_stack::Result<chrono::DateTime<chrono::Utc>, ErrorStack> {
    // Pop the "GMT" off the end of the string so chrono can parse it
    let expire_asn1time_trimmed = expire_asn1time_str.trim_end_matches(" GMT");

    let expire_naive_chrono =
        chrono::NaiveDateTime::parse_from_str(expire_asn1time_trimmed, "%b %d %H:%M:%S %Y")
            .change_context(ErrorStack::ConvertAsn1TimeToChrono)?;

    Ok(chrono::Utc.from_utc_datetime(&expire_naive_chrono))
}

async fn days_til_expiry(expiration: chrono::DateTime<chrono::Utc>) -> i64 {
    let now = chrono::Utc::now();
    let duration = expiration - now;
    duration.num_days()
}
