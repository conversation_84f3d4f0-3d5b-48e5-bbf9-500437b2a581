use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetConfig,
    GetCurrentAccount,
    GetEtcPublicEcrs,
    SelectFromDocker,
    NotOnePublicEcrFound(crate::EcrPublic, usize),
}

#[derive(Debug)]
pub struct EcrPublic {
    pub graphkey: crate::EcrPublic,
    pub repository_name: String,
    pub arn: String,
    pub source: String,
}

impl crate::GraphValueExt for EcrPublic {}

pub async fn get(
    gk_ecr: crate::EcrPublic,
    app: &ace_db::App,
) -> error_stack::Result<EcrPublic, ErrorStack> {
    let filter = crate::EcrPublicFilter::One(gk_ecr.clone());

    let mut ecrs = select(&filter, app).await?;
    if ecrs.len() == 1 {
        return Ok(ecrs.pop().unwrap());
    }
    error_stack::bail!(ErrorStack::NotOnePublicEcrFound(gk_ecr, ecrs.len()));
}

pub async fn select(
    filter: &crate::EcrPublicFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<EcrPublic>, ErrorStack> {
    let mut rval = Vec::new();

    for bucket in select_result(filter, app).await? {
        match bucket {
            Ok(bucket) => rval.push(bucket),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::EcrPublicFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<EcrPublic, ErrorStack>>, ErrorStack> {
    let etc_filter = match filter {
        crate::EcrPublicFilter::All => ace_db::Filter::All,
        crate::EcrPublicFilter::One(ecr) => match ecr {
            crate::EcrPublic::Db(name) => ace_db::Filter::One(name.clone()),
            crate::EcrPublic::Docker(_) => ace_db::Filter::None,
        },
        crate::EcrPublicFilter::None => ace_db::Filter::None, // DO NOT exit early, may be dockers.
    };

    let mut rval = Vec::new();

    // Collect/process Ecrs from Etc
    let etc_pub_ecrs = ace_db::ecr_public::select_result(etc_filter, ace_db_app)
        .await
        .change_context(ErrorStack::GetEtcPublicEcrs)?;

    // Get config/account info for construction:
    let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let account = crate::aws_account::get(&crate::AwsAccount::Db(config.account_key), ace_db_app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    for ecr in etc_pub_ecrs {
        match ecr {
            Ok(ecr) => {
                rval.push(Ok(construct_from_etc(ecr, &account.aws_account_id)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    // Collect/process Ecrs from Docker
    rval.extend(
        crate::docker::select_result_ecr_public(filter, ace_db_app)
            .await
            .change_context(ErrorStack::SelectFromDocker)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDocker)),
    );

    Ok(rval)
}

fn construct_from_etc(
    ecr_pub: ace_db::etc::ecr_public::EcrPublicRepo,
    aws_account_id: &str,
) -> EcrPublic {
    let graphkey = crate::EcrPublic::Db(ecr_pub.name.clone());
    let arn = format!(
        "arn:aws:ecr-public::{aws_account_id}:repository/{}",
        ecr_pub.name
    );

    EcrPublic {
        graphkey,
        repository_name: ecr_pub.name,
        arn,
        source: ecr_pub.source.to_string_lossy().to_string(),
    }
}
