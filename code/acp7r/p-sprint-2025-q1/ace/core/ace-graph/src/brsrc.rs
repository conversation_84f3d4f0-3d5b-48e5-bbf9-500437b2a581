use crate::GraphKeyExt;
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAwsAccountFromDb(crate::Brsrc),
    GetBrsrc(crate::Brsrc),
    NotOneFound(crate::Brsrc),
    SelectFromDb,
}

#[derive(Debug)]
pub struct Brsrc {
    pub graphkey: crate::Brsrc,
    pub name: String,
    pub source: Source,
    pub target: Target,
    pub file_source: std::path::PathBuf,
}

impl crate::GraphValueExt for Brsrc {}

impl crate::GraphKeyName for crate::Brsrc {
    fn get_name(&self) -> String {
        match self {
            crate::Brsrc::Db(name) => name.clone(),
        }
    }
}

#[derive(Debug)]
pub struct Source {
    pub bucket: String,
    pub bucket_arn: String,
}

impl crate::GraphValueExt for Source {}

#[derive(Debug)]
pub struct Target {
    pub bucket: String,
    pub bucket_arn: String,
    pub account_key: String,
    pub region: String,
    pub aws_account_id: String,
}

impl crate::GraphValueExt for Target {}

pub async fn get(
    gk_brsrc: &crate::Brsrc,
    app: &ace_db::App,
) -> error_stack::Result<Brsrc, ErrorStack> {
    let filter = crate::BrsrcFilter::One(gk_brsrc.clone());
    let mut brsrcs = select_result(&filter, app).await?;

    // Validate that one record was returned
    let brsrc: Result<Brsrc, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if brsrcs.len() == 1 {
            brsrcs.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_brsrc.clone()))
        }
    };

    brsrc.change_context_lazy(|| ErrorStack::GetBrsrc(gk_brsrc.to_owned()))
}

pub async fn select(
    filter: &crate::BrsrcFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Brsrc>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for brsrc in select_result(filter, app).await? {
        match brsrc {
            Ok(brsrc) => {
                rval.push(brsrc);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::BrsrcFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Brsrc, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::BrsrcFilter::All => ace_db::Filter::All,
        crate::BrsrcFilter::One(crate::Brsrc::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::BrsrcFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let brsrcs = ace_db::brsrc::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for brsrc in brsrcs {
        match brsrc {
            Ok(brsrc) => {
                let brsrc = construct(brsrc, app).await;
                rval.push(brsrc);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_iam_role(
    filter: &crate::IamRoleFilter,
    app: &ace_db::App,
    config: &crate::config::Config,
) -> error_stack::Result<Vec<error_stack::Result<crate::iam_role::IamRole, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let brsrc_filter = match filter {
        crate::IamRoleFilter::All => crate::BrsrcFilter::All,
        crate::IamRoleFilter::One(crate::IamRole::Brsrc(gk_brsrc)) => match gk_brsrc {
            crate::Brsrc::Db(name) => crate::BrsrcFilter::One(crate::Brsrc::Db(name.clone())),
        },
        _ => return Ok(rval), // exit early
    };

    for brsrc_result in select_result(&brsrc_filter, app).await? {
        match brsrc_result {
            Ok(brsrc) => {
                // VERY IMPORTANT that this matches the code that generates it the source side
                let iam_role_name = format!(
                    "{}-{}-{}-brsrc",
                    config.account_key, config.region, brsrc.name
                );

                let iam_role = crate::iam_role::IamRole {
                    graphkey: crate::IamRole::Brsrc(brsrc.graphkey.clone()),
                    name: iam_role_name,
                    role_type: crate::iam_role::IamRoleType::New {
                        assume_role_policy: serde_json::json!(
                            {
                                "Version": "2012-10-17",
                                "Statement": [
                                    {
                                        "Action": "sts:AssumeRole",
                                        "Principal": {
                                            "Service": "s3.amazonaws.com"
                                        },
                                        "Effect": "Allow",
                                        "Sid": ""
                                    },
                                    {
                                        "Effect": "Allow",
                                        "Principal": {
                                            "Service": "batchoperations.s3.amazonaws.com"
                                        },
                                        "Action": "sts:AssumeRole"
                                    }
                                ]
                            }
                        ),
                    },
                    with_instance_profile: false,
                    policy_statements: Vec::new(),
                    source: brsrc.graphkey.serialize(),
                };

                rval.push(Ok(iam_role));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    brsrc: ace_db::etc::brsrc::BrSrc,
    app: &ace_db::App,
) -> error_stack::Result<Brsrc, ErrorStack> {
    let graphkey = crate::Brsrc::Db(brsrc.name.clone());

    let desired_account_key = &brsrc.target.account_key;
    let aws_account =
        crate::aws_account::get(&crate::AwsAccount::Db(desired_account_key.to_owned()), app)
            .await
            .change_context(ErrorStack::GetAwsAccountFromDb(graphkey.clone()))?;

    let source = Source {
        bucket: brsrc.source.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brsrc.source.bucket),
    };

    let target = Target {
        bucket: brsrc.target.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brsrc.target.bucket),
        account_key: aws_account.account_key.clone(),
        region: brsrc.target.region.clone(),
        aws_account_id: aws_account.aws_account_id,
    };

    Ok(Brsrc {
        graphkey,
        name: brsrc.name,
        source,
        target,
        file_source: brsrc.file_source,
    })
}
