#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneBucketVersionFound(crate::BucketVersioning, usize),
}

#[derive(Debug)]
pub struct BucketVersion {
    pub graphkey: crate::BucketVersioning,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for BucketVersion {}

pub async fn get(
    gk_bucket_vers: crate::BucketVersioning,
) -> error_stack::Result<BucketVersion, ErrorStack> {
    let filter = crate::BucketVersioningFilter::One(gk_bucket_vers.clone());

    let mut b_vers = select(filter).await?;
    if b_vers.len() == 1 {
        return Ok(b_vers.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneBucketVersionFound(
        gk_bucket_vers,
        b_vers.len()
    ));
}

pub async fn select(
    filter: crate::BucketVersioningFilter,
) -> error_stack::Result<Vec<BucketVersion>, ErrorStack> {
    let mut rval = Vec::new();

    for b_vers in select_result(filter).await? {
        match b_vers {
            Ok(bv) => rval.push(bv),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::BucketVersioningFilter,
) -> error_stack::Result<Vec<error_stack::Result<BucketVersion, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
