#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneLocalFileFound(crate::LocalFile, usize),
}

#[derive(Debug)]
pub struct LocalFile {
    pub graphkey: crate::LocalFile,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for LocalFile {}

pub async fn get(gk_ins_profile: crate::LocalFile) -> error_stack::Result<LocalFile, ErrorStack> {
    let filter = crate::LocalFileFilter::One(gk_ins_profile.clone());

    let mut lfs = select(filter).await?;
    if lfs.len() == 1 {
        return Ok(lfs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneLocalFileFound(gk_ins_profile, lfs.len()));
}

pub async fn select(
    filter: crate::LocalFileFilter,
) -> error_stack::Result<Vec<LocalFile>, ErrorStack> {
    let mut rval = Vec::new();

    for loc_file in select_result(filter).await? {
        match loc_file {
            Ok(l) => rval.push(l),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::LocalFileFilter,
) -> error_stack::Result<Vec<error_stack::Result<LocalFile, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
