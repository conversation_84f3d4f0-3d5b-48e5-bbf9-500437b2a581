#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneNatGatewayFound(crate::NatGateway, usize),
}

#[derive(Debug)]
pub struct NatGateway {
    pub graphkey: crate::<PERSON><PERSON>ate<PERSON>,
    pub name: String,
    pub source: String,
    // Other information...
}

impl crate::GraphValueExt for NatGateway {}

pub async fn get(gk_natgateway: crate::NatGateway) -> error_stack::Result<NatGateway, ErrorStack> {
    let filter = crate::NatGatewayFilter::One(gk_natgateway.clone());

    let mut nat_gateways = select(filter).await?;
    if nat_gateways.len() == 1 {
        return Ok(nat_gateways.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneNatGatewayFound(
        gk_natgateway,
        nat_gateways.len()
    ));
}

pub async fn select(
    filter: crate::NatGatewayFilter,
) -> error_stack::Result<Vec<NatGateway>, ErrorStack> {
    let mut rval = Vec::new();

    for nat_gateway in select_result(filter).await? {
        match nat_gateway {
            Ok(ng) => rval.push(ng),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::NatGatewayFilter,
) -> error_stack::Result<Vec<error_stack::Result<NatGateway, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
