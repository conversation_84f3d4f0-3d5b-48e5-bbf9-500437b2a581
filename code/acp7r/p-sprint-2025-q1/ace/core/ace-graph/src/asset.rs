use error_stack::ResultExt;
use std::path::PathBuf;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConstructAssetFromDb,
    DbError,
    GetAsset(crate::Asset),
    InvalidAssetName(String),
    NotOneFound(crate::Asset),
    SelectFromDb,
}

#[derive(Debug, Clone)]
pub struct Asset {
    pub graphkey: crate::Asset,
    pub name: String,

    pub version: String,
    pub target: String,
    pub hash: String,

    pub source: PathBuf,
}

impl crate::GraphValueExt for Asset {}

pub async fn get_latest_asset_with_target(
    asset_gk: crate::Asset,
    app: &ace_db::App,
) -> error_stack::Result<Asset, ErrorStack> {
    let filter = crate::AssetFilter::LatestSpecificAssetAndTarget(asset_gk.clone());
    let mut assets = select_result(&filter, app).await?;

    // Validate that one record was returned
    let asset: Result<Asset, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if assets.len() == 1 {
            assets.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(asset_gk))
        }
    };

    asset.change_context_lazy(|| ErrorStack::GetAsset(asset_gk))
}

pub async fn select(
    filter: &crate::AssetFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<Asset>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for asset in select_result(filter, ace_db_app).await? {
        match asset {
            Ok(asset) => {
                rval.push(asset);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AssetFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Asset, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::AssetFilter::All => ace_db::local_asset::AssetFilter::All,
        crate::AssetFilter::None => ace_db::local_asset::AssetFilter::None,
        crate::AssetFilter::AllLatest => ace_db::local_asset::AssetFilter::AllLatest,
        crate::AssetFilter::AllLatestSpecificTarget(asset_gk) => {
            ace_db::local_asset::AssetFilter::AllLatestTarget(asset_gk.clone())
        }
        crate::AssetFilter::AllLatestSpecificAsset(asset_gk) => {
            ace_db::local_asset::AssetFilter::AllLatestSpecificName(asset_gk.name())
        }

        crate::AssetFilter::AllSpecificAssetName(asset_gk) => {
            ace_db::local_asset::AssetFilter::AllSpecificName(asset_gk.name())
        }
        crate::AssetFilter::AllSpecificTarget(target) => {
            ace_db::local_asset::AssetFilter::AllSpecificTarget(target.clone())
        }
        crate::AssetFilter::AllSpecificVersion(version) => {
            ace_db::local_asset::AssetFilter::AllSpecificVersion(version.clone())
        }

        crate::AssetFilter::LatestSpecificAssetAndTarget(asset_gk) => {
            ace_db::local_asset::AssetFilter::LatestSpecificNameAndTarget(
                asset_gk.name(),
                asset_gk.target(),
            )
        }
        crate::AssetFilter::OneExactAsset(asset_gk) => {
            ace_db::local_asset::AssetFilter::OneExactAsset(
                asset_gk.name(),
                asset_gk.target(),
                asset_gk.version(),
            )
        }
    };

    let mut rval = Vec::new();

    let assets = ace_db::asset::select_result(db_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for asset in assets {
        match asset {
            Ok(asset) => {
                rval.push(construct(asset).change_context(ErrorStack::ConstructAssetFromDb));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(asset: ace_db::local_asset::Asset) -> error_stack::Result<Asset, ErrorStack> {
    let target = asset.target.clone();
    let version = asset.version.to_string().clone();

    let graphkey = match asset.bin_name.as_str() {
        "ace-agent" => crate::Asset::AceAgent(target, version),
        "ace-agent-updater" => crate::Asset::AceAgentUpdater(target, version),
        "mp-process" => crate::Asset::MediaproctorProcess(target, version),
        "mp-stream" => crate::Asset::MediaproctorStream(target, version),
        _ => {
            error_stack::bail!(ErrorStack::InvalidAssetName(asset.bin_name.clone()))
        }
    };

    Ok(Asset {
        graphkey,
        name: asset.bin_name,
        version: asset.version.to_string(),
        target: asset.target,
        hash: asset.hash,
        source: asset.path,
    })
}
