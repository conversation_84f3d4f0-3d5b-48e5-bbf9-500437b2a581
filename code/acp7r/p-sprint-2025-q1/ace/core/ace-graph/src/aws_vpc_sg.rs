use super::ins::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, In<PERSON><PERSON>, <PERSON>Rang<PERSON>};
use crate::GraphKeyExt;
use error_stack::ResultExt;
use granite::dash;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetConfig,
    NotOneSecurityGroupFound(crate::AwsVpcSg, usize),
    SelectFromAwsElasticacheServerless,
    SelectFromAwsElb,
    SelectFromAwsRds,
    SelectFromDb,
    SelectFromInstance,
}

#[derive(Debug)]
pub struct AwsVpcSecurityGroup {
    pub graphkey: crate::AwsVpcSg,
    pub name: String,
    pub ingress: Vec<crate::ins::Ingress>,
    pub description: String,
    pub source: String,
}

impl AwsVpcSecurityGroup {
    /// Returns a tuple of (Resource ID, Resource Name, Tag Name)
    pub fn terraform_resource(&self, config: &crate::config::Config) -> (String, String, String) {
        match &self.graphkey {
            crate::AwsVpcSg::AwsEcSls(_aws_elasticache_serverless) => {
                let dashed_gk = self.graphkey.serialize_dashed();
                (dashed_gk.clone(), dashed_gk.clone(), dashed_gk)
            }

            crate::AwsVpcSg::AwsElb(_aws_elb) => {
                let dashed_gk = self.graphkey.serialize_dashed();
                (dashed_gk.clone(), dashed_gk.clone(), dashed_gk)
            }

            crate::AwsVpcSg::AwsRds(_aws_rds_instance) => {
                let dashed_gk = self.graphkey.serialize_dashed();
                (dashed_gk.clone(), dashed_gk.clone(), dashed_gk)
            }

            crate::AwsVpcSg::Db(_name) => {
                let serialized_gk = self.graphkey.serialize_dashed();

                (
                    serialized_gk.clone(),
                    serialized_gk.clone(),
                    format!("{}-{}", config.account_key, serialized_gk),
                )
            }

            crate::AwsVpcSg::Instance(ins_gk) => {
                let dashed_instance_name = dash(&ins_gk.map_name_to_terraform_resource_name());
                let resource_name = format!("{}-{}-sg", config.account_key, dashed_instance_name);

                let resource_id = if matches!(ins_gk, crate::Instance::Developer(_)) {
                    dash(&ins_gk.map_name_to_terraform_resource_name())
                } else {
                    format!("{}_sg", dash(&ins_gk.map_name_to_terraform_resource_name()))
                };

                (resource_id, resource_name.clone(), resource_name)
            }

            crate::AwsVpcSg::MediaProctor => (
                "mediaproctor_sg".to_string(),
                self.name.clone(),
                self.name.clone(),
            ),
        }
    }

    pub fn to_terraform_resource_id(&self, config: &crate::config::Config) -> String {
        self.terraform_resource(config).0
    }
}

impl crate::GraphValueExt for AwsVpcSecurityGroup {}

pub async fn get(
    gk_sg: &crate::AwsVpcSg,
    app: &ace_db::App,
) -> error_stack::Result<AwsVpcSecurityGroup, ErrorStack> {
    let filter = crate::AwsVpcSecurityGroupFilter::One(gk_sg.clone());

    let mut sgs = select(&filter, app).await?;
    if sgs.len() == 1 {
        return Ok(sgs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneSecurityGroupFound(
        gk_sg.clone(),
        sgs.len()
    ));
}

pub async fn select(
    filter: &crate::AwsVpcSecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<AwsVpcSecurityGroup>, ErrorStack> {
    let mut rval = Vec::new();

    for sg in select_result(filter, app).await? {
        match sg {
            Ok(sg) => {
                rval.push(sg);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AwsVpcSecurityGroupFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsVpcSecurityGroup, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let (db_filter, include_mediaproctor) = match filter {
        crate::AwsVpcSecurityGroupFilter::All => (ace_db::Filter::All, true),
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::AwsEcSls(
            _aws_elasticache_serverless,
        )) => (ace_db::Filter::None, false),
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::AwsRds(_aws_rds_instance)) => {
            (ace_db::Filter::None, false)
        }
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::AwsElb(_aws_elb)) => {
            (ace_db::Filter::None, false)
        }
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::Db(name)) => {
            (ace_db::Filter::One(name.to_owned()), false)
        }
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::Instance(_ins_gk)) => {
            (ace_db::Filter::None, false)
        }
        crate::AwsVpcSecurityGroupFilter::One(crate::AwsVpcSg::MediaProctor) => {
            (ace_db::Filter::None, true)
        }
        crate::AwsVpcSecurityGroupFilter::None => return Ok(Vec::new()), // exit early
    };

    let db_sgs = ace_db::aws_vpc_sg::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for sg in db_sgs {
        match sg {
            Ok(sg) => {
                rval.push(Ok(construct(sg)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    rval.extend(
        crate::aws_ec_sls::select_result_securitygroup(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromAwsElasticacheServerless)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromAwsElasticacheServerless)),
    );

    rval.extend(
        crate::aws_rds::select_result_aws_vpc_sg(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromAwsRds)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromAwsRds)),
    );

    rval.extend(
        crate::aws_elb::select_result_aws_vpc_sg(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromAwsElb)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromAwsElb)),
    );

    rval.extend(
        crate::ins::select_result_securitygroup(filter, app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromInstance)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromInstance)),
    );

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    if include_mediaproctor {
        let mp_ingress = vec![
            // Allow inbound SSH traffic from vpc
            crate::ins::Ingress {
                ports: crate::ins::PortRange::One(22),
                protocol: "tcp".to_string(),
                cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
            },
            // Allow inbound SSH traffic from my ip
            crate::ins::Ingress {
                ports: crate::ins::PortRange::One(22),
                protocol: "tcp".to_string(),
                cidr_blocks: crate::ins::CidrBlocks::Known(vec![config.my_ipnetwork]),
            },
        ];

        rval.push(Ok(AwsVpcSecurityGroup {
            graphkey: crate::AwsVpcSg::MediaProctor,
            name: format!("{}-mediaproctor-sg", &config.account_key),
            ingress: mp_ingress,
            description: "for mediaproctor process and stream servers".to_string(),
            source: "builtin".to_string(),
        }));
    }

    Ok(rval)
}

fn construct(sg: ace_db::etc::aws_vpc_sg::AwsVpcSecurityGroup) -> AwsVpcSecurityGroup {
    let mut ingress = vec![];

    for sg_ingress in sg.ingress {
        ingress.push(Ingress {
            protocol: sg_ingress.protocol,
            ports: match sg_ingress.ports {
                ace_db::etc::instance::PortRange::One(port) => PortRange::One(port),
                ace_db::etc::instance::PortRange::Range(from, to) => PortRange::Range(from, to),
                ace_db::etc::instance::PortRange::All => PortRange::All,
            },
            cidr_blocks: CidrBlocks::Known(sg_ingress.cidr_blocks),
        });
    }

    AwsVpcSecurityGroup {
        graphkey: crate::AwsVpcSg::Db(sg.name.clone()),
        name: sg.name,
        ingress,
        description: sg.description,
        source: sg.source.to_string_lossy().to_string(),
    }
}
