use error_stack::ResultExt;
use std::collections::HashSet;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    ExtractAmiIdFromPackerArtifactId(String),
    GetCurrentEnvInfo,
    InvalidPacker(String),
    NotOneAmiFound(crate::Ami, usize),
    SelectPackerTemplates,
}

#[derive(Debug)]
pub struct Ami {
    pub graphkey: crate::Ami,
    pub account_key: String,
    pub name: String,
    pub region: String,
    pub ami_id: String,
    pub build_timestamp: Option<u64>,
    pub source: std::path::PathBuf,
}

impl crate::GraphValueExt for Ami {}

pub async fn get(
    gk_ami: crate::Ami,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Ami, ErrorStack> {
    let filter = crate::AmiFilter::One(gk_ami.clone());

    let mut amis = select(filter, ace_db_app).await?;
    if amis.len() == 1 {
        return Ok(amis.pop().unwrap());
    }
    error_stack::bail!(ErrorStack::NotOneAmiFound(gk_ami, amis.len()))
}

/// Passes filter on to `select_result()` and processes the results.
/// Bails at any error found.
pub async fn select(
    filter: crate::AmiFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<Ami>, ErrorStack> {
    let mut rval = vec![];

    // May seem redundant now, but may not be later when ami's can exist on disk?
    for ami_result in select_result(filter, ace_db_app).await? {
        match ami_result {
            Ok(ami) => rval.push(ami),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

#[allow(clippy::too_many_lines)]
pub async fn select_result(
    filter: crate::AmiFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Ami, ErrorStack>>, ErrorStack> {
    let mut rval = vec![];

    let (etc_filter, pkr_filter) = match &filter {
        crate::AmiFilter::All => (ace_db::Filter::All, crate::PackerFilter::All),
        crate::AmiFilter::AllLatest => (ace_db::Filter::All, crate::PackerFilter::All),
        crate::AmiFilter::One(gk_ami) => {
            match gk_ami {
                crate::Ami::Packer(gk_packer) => {
                    // Implies latest
                    (
                        ace_db::Filter::None,
                        crate::PackerFilter::One(gk_packer.clone()),
                    )
                }
                crate::Ami::PackerManifest(gk_packer, _packer_run_uuid) => {
                    // A specific, likely archived packer build
                    (
                        ace_db::Filter::None,
                        crate::PackerFilter::One(gk_packer.clone()),
                    )
                }
                crate::Ami::Db(name) => (
                    ace_db::Filter::One(name.to_string()),
                    crate::PackerFilter::None,
                ),
            }
        }
        crate::AmiFilter::None => return Ok(rval),
    };

    let mut latest_images = Vec::new();
    let mut all_images = Vec::new();
    let mut template_names = HashSet::new();

    let relevant_packer_templates = crate::pkr::select_result(pkr_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectPackerTemplates)?;

    // Collect valid build names to plumb packer_manifest.json for...
    for packer in relevant_packer_templates {
        match packer {
            Ok(pkr) => {
                template_names.insert(pkr.name.clone());
            }
            Err(e) => {
                return Err(e.change_context(ErrorStack::DbError));
            }
        }
    }

    // Construct packer.manifest.json filters
    // (Can't do this in first match statement because we need the names from ace_graph::packer...)
    let manifest_filter: Vec<ace_db::PackerManifestFilter> = match &filter {
        crate::AmiFilter::All => {
            vec![
                ace_db::PackerManifestFilter::All(template_names.clone()),
                ace_db::PackerManifestFilter::LatestEachByName(template_names.clone()),
            ]
        }
        crate::AmiFilter::AllLatest => {
            vec![ace_db::PackerManifestFilter::LatestEachByName(
                template_names.clone(),
            )]
        }
        crate::AmiFilter::One(gk_ami) => match gk_ami {
            crate::Ami::Packer(gk_packer) => {
                vec![ace_db::PackerManifestFilter::LatestOneByName(
                    gk_packer.variant_to_name(),
                )]
            }
            crate::Ami::PackerManifest(gk_packer, packer_run_uuid) => {
                vec![ace_db::PackerManifestFilter::One(
                    gk_packer.variant_to_name(),
                    packer_run_uuid.to_owned(),
                )]
            }
            crate::Ami::Db(_name) => vec![ace_db::PackerManifestFilter::None],
        },
        crate::AmiFilter::None => return Ok(Vec::new()),
    };

    // Plumb ace_db::data::packer_manifest
    // (match on manifest filters to decide which vec the images are stored in (necessary for later processing))
    for filter in manifest_filter {
        match &filter {
            ace_db::PackerManifestFilter::All(_names) => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::AllByName(_name) => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::LatestEachByName(_names) => latest_images.extend(
                ace_db::data::packer_manifest::select(&filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::One(_, _) => all_images.extend(
                ace_db::data::packer_manifest::select(&filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::LatestOneByName(_name) => latest_images.extend(
                ace_db::data::packer_manifest::select(&filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::DbError)?,
            ),
            ace_db::PackerManifestFilter::None => continue, // Do nothing
        }
    }

    // Collect etc amis
    let etc_amis = ace_db::ami::select_result(etc_filter, ace_db_app)
        .await
        .change_context(ErrorStack::DbError)?;

    // Get current account_key and region
    let env_info =
        ace_db::current_account_key_and_region().change_context(ErrorStack::GetCurrentEnvInfo)?;

    // Process etc amis
    for image in etc_amis {
        match image {
            Ok(ami) => {
                rval.push(Ok(construct_etc_ami(
                    ami,
                    &env_info.account_key,
                    &env_info.region,
                )));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    let manifest_path = &ace_db_app.packer_manifest_path;

    // Process latest_images
    for image in latest_images {
        rval.push(construct_from_latest_pkr_image(
            image,
            &env_info.account_key,
            &env_info.region,
            manifest_path,
            &template_names,
        ));
    }

    // Process all_images
    for image in all_images {
        rval.push(construct_from_specific_image(
            image,
            &env_info.account_key,
            &env_info.region,
            manifest_path,
            &template_names,
        ));
    }

    Ok(rval)
}

fn extract_ami_id(artifact_id: &str) -> Option<String> {
    let mut artifacts = artifact_id.split(',');
    let artifact = artifacts.next()?;
    let mut parts = artifact.splitn(2, ':');
    let _artifact_region = parts.next()?.to_string();
    let artifact_ami_id = parts.next()?.to_string();

    Some(artifact_ami_id)
}

fn match_name_to_packer(
    name: &str,
    valid_names: &HashSet<String>,
) -> error_stack::Result<crate::Packer, ErrorStack> {
    if valid_names.contains(name) {
        match name {
            "ubuntu-22-04-ace2" => Ok(crate::Packer::Ubuntu2204Ace2),
            "ubuntu-22-04-devbox" => Ok(crate::Packer::Ubuntu2204Devbox),
            "ubuntu-22-04-docker" => Ok(crate::Packer::Ubuntu2204Docker),
            "ubuntu-22-04-openvpn" => Ok(crate::Packer::Ubuntu2204Openvpn),
            "ubuntu-22-04-postgresql" => Ok(crate::Packer::Ubuntu2204Postgresql),
            "ubuntu-22-04-videoproc" => Ok(crate::Packer::Ubuntu2204Videoproc),
            _ => Ok(crate::Packer::Db(name.to_string())),
        }
    } else {
        // Shouldn't technically be possible...what with how the processing is done.
        Err(error_stack::report!(ErrorStack::InvalidPacker(
            name.to_string()
        )))
    }
}

fn construct_etc_ami(ami: ace_db::etc::ami::Ami, account_key: &str, region: &str) -> Ami {
    let name = ami.name.clone();

    Ami {
        name: ami.name,
        graphkey: crate::Ami::Db(name),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id: ami.ami_id,
        build_timestamp: None,
        source: ami.source,
    }
}

fn construct_from_latest_pkr_image(
    pkr: ace_db::data::packer_manifest::SerdeBuild,
    account_key: &str,
    region: &str,
    source_path: &std::path::Path,
    valid_names: &HashSet<String>,
) -> error_stack::Result<Ami, ErrorStack> {
    let Some(ami_id) = extract_ami_id(&pkr.artifact_id) else {
        return Err(error_stack::report!(
            ErrorStack::ExtractAmiIdFromPackerArtifactId(pkr.artifact_id.clone())
        ));
    };

    let packer_graphkey = match_name_to_packer(&pkr.name, valid_names)?;

    Ok(Ami {
        name: pkr.name,
        graphkey: crate::Ami::Packer(packer_graphkey),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id,
        build_timestamp: Some(pkr.build_time),
        source: source_path.to_path_buf(),
    })
}

fn construct_from_specific_image(
    pkr: ace_db::data::packer_manifest::SerdeBuild,
    account_key: &str,
    region: &str,
    source_path: &std::path::Path,
    valid_names: &HashSet<String>,
) -> error_stack::Result<Ami, ErrorStack> {
    let Some(ami_id) = extract_ami_id(&pkr.artifact_id) else {
        return Err(error_stack::report!(
            ErrorStack::ExtractAmiIdFromPackerArtifactId(pkr.artifact_id.clone())
        ));
    };

    let packer_graphkey = match_name_to_packer(&pkr.name, valid_names)?;

    Ok(Ami {
        name: pkr.name,
        graphkey: crate::Ami::PackerManifest(packer_graphkey, pkr.packer_run_uuid.clone()),
        account_key: account_key.to_string(),
        region: region.to_string(),
        ami_id,
        build_timestamp: Some(pkr.build_time),
        source: source_path.to_path_buf(),
    })
}
