use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetDomain(crate::Domain),
    NotOneFound(crate::Domain),
    SelectFromDb,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct Domain {
    pub graphkey: crate::Domain,
    pub name: String,
    pub aws: Aws,
    pub subdomains: Vec<Subdomain>,
    pub source: std::path::PathBuf,
}

impl crate::GraphValueExt for Domain {}

pub type DomainList = Vec<Domain>;

#[derive(Debug, Clone)]
pub struct Aws {
    pub hosted_zone_id: String,
}

impl crate::GraphValueExt for Aws {}

#[derive(Debug, <PERSON>lone)]
pub struct Subdomain {
    pub name: String,
    pub tls: bool,
    pub wildcard: bool,
    pub cname: Option<String>,
}

impl crate::GraphValueExt for Subdomain {}

pub async fn get(
    gk_domain: &crate::Domain,
    app: &ace_db::App,
) -> error_stack::Result<Domain, ErrorStack> {
    let filter = crate::DomainFilter::One(gk_domain.clone());
    let mut domains = select_result(&filter, app).await?;

    // Validate that one record was returned
    let domain: Result<Domain, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if domains.len() == 1 {
            domains.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_domain.clone()))
        }
    };

    domain.change_context_lazy(|| ErrorStack::GetDomain(gk_domain.to_owned()))
}

pub async fn select(
    filter: &crate::DomainFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Domain>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for domain in select_result(filter, app).await? {
        match domain {
            Ok(domain) => {
                rval.push(domain);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DomainFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Domain, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::DomainFilter::All => ace_db::Filter::All,
        crate::DomainFilter::One(crate::Domain::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::DomainFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let domains = ace_db::domain::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for domain in domains {
        match domain {
            Ok(domain) => {
                let domain = construct(domain).await;
                rval.push(domain);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

async fn construct(domain: ace_db::etc::domain::Domain) -> error_stack::Result<Domain, ErrorStack> {
    let mut subdomains = Vec::new();
    for subdomain in domain.subdomain {
        subdomains.push(Subdomain {
            name: subdomain.name,
            tls: subdomain.tls,
            wildcard: subdomain.wildcard,
            cname: subdomain.cname,
        });
    }

    Ok(Domain {
        graphkey: crate::Domain::Db(domain.name.clone()),
        name: domain.name,
        aws: Aws {
            hosted_zone_id: domain.aws.hosted_zone_id,
        },
        subdomains,
        source: domain.source,
    })
}
