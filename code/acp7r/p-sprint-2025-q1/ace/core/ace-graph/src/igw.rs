#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamUserFound(crate::InternetGateway, usize),
}

#[derive(Debug)]
pub struct InternetGateway {
    pub graphkey: crate::InternetGateway,
    pub name: String,
    pub vpc: crate::Vpc,
    pub source: String,
}

impl crate::GraphValueExt for InternetGateway {}

pub async fn get(
    gk_int_gateway: crate::InternetGateway,
) -> error_stack::Result<InternetGateway, ErrorStack> {
    let filter = crate::IgwFilter::One(gk_int_gateway.clone());

    let mut internet_gateway = select(filter).await?;
    if internet_gateway.len() == 1 {
        return Ok(internet_gateway.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamUserFound(
        gk_int_gateway,
        internet_gateway.len()
    ));
}

pub async fn select(
    filter: crate::IgwFilter,
) -> error_stack::Result<Vec<InternetGateway>, ErrorStack> {
    let mut rval = Vec::new();

    for int_gateway in select_result(filter).await? {
        match int_gateway {
            Ok(ig) => rval.push(ig),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IgwFilter,
) -> error_stack::Result<Vec<error_stack::Result<InternetGateway, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
