use crate::GraphKeyExt;
use crate::GraphKeyName;
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneInstanceProfileFound(crate::InstanceProfile, usize),
    SelectFromIamRole,
    IndividualError,
}

#[derive(Debug)]
pub struct InstanceProfile {
    pub graphkey: crate::InstanceProfile,
    pub name: String,
    pub source: String,
}

// We cannot have InstanceProfiles UNLESS the IamRole struct has `with_instance_profile` enabled.
// No need to check for that.  If it's there, it's valid.
impl InstanceProfile {
    /// Returns (Resource, ResourceName, Tags)
    /// Only InstanceProfile::IamRole::Db() get tags.
    pub fn terraform_resource(&self, config: &crate::config::Config) -> (String, String, bool) {
        match &self.graphkey {
            crate::InstanceProfile::IamRole(crate::IamRole::App(app_gk)) => (
                crate::IamRole::App(app_gk.clone()).serialize_dashed(),
                format!(
                    "{}-{}-app-{}-0",
                    config.account_key,
                    config.region,
                    app_gk.get_name()
                ),
                false,
            ),
            crate::InstanceProfile::IamRole(crate::IamRole::AppPreview(app_gk)) => (
                crate::IamRole::AppPreview(app_gk.clone()).serialize_dashed(),
                format!(
                    "{}-{}-app-{}-1",
                    config.account_key,
                    config.region,
                    app_gk.get_name()
                ),
                false,
            ),
            crate::InstanceProfile::IamRole(crate::IamRole::Brsrc(brsrc_gk)) => {
                let dashed_gk = brsrc_gk.serialize_dashed();
                (dashed_gk.clone(), dashed_gk, false)
            }
            crate::InstanceProfile::IamRole(crate::IamRole::Deploy) => (
                crate::IamRole::Deploy.serialize_dashed(),
                format!("{}-{}-deploy", config.account_key, config.region),
                false,
            ),
            crate::InstanceProfile::IamRole(crate::IamRole::Developer(dev_gk)) => {
                let dev_name = dev_gk.get_name();
                (
                    format!("developer-{}", dev_name),
                    format!(
                        "{}-{}-developer-{}",
                        config.account_key, config.region, dev_name
                    ),
                    false,
                )
            }
            crate::InstanceProfile::IamRole(crate::IamRole::Db(_name)) => {
                let dashed_gk = self.graphkey.serialize_dashed();
                (dashed_gk.clone(), dashed_gk, true)
            }
        }
    }
}

impl crate::GraphValueExt for InstanceProfile {}

pub async fn get(
    gk_ins_profile: crate::InstanceProfile,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<InstanceProfile, ErrorStack> {
    let filter = crate::InstanceProfileFilter::One(gk_ins_profile.clone());

    let mut ins_profiles = select(filter, ace_db_app).await?;
    if ins_profiles.len() == 1 {
        return Ok(ins_profiles.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneInstanceProfileFound(
        gk_ins_profile,
        ins_profiles.len()
    ));
}

pub async fn select(
    filter: crate::InstanceProfileFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<InstanceProfile>, ErrorStack> {
    let mut rval = Vec::new();

    for inst_profiles in select_result(&filter, ace_db_app).await? {
        match inst_profiles {
            Ok(i) => rval.push(i),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::InstanceProfileFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<InstanceProfile, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    // Only consults IamRole.
    // For now, there is no other type or dependency.

    let iam_role_filter = match filter {
        crate::InstanceProfileFilter::All => crate::IamRoleFilter::All,
        crate::InstanceProfileFilter::One(gk) => match gk {
            crate::InstanceProfile::IamRole(gk_iam_role) => {
                crate::IamRoleFilter::One(gk_iam_role.clone())
            }
        },
        crate::InstanceProfileFilter::None => crate::IamRoleFilter::None,
    };

    let instance_profiles =
        crate::iam_role::select_result_instance_profile(iam_role_filter, ace_db_app)
            .await
            .change_context(ErrorStack::SelectFromIamRole)?;

    for profile in instance_profiles {
        match profile {
            Ok(p) => rval.push(Ok(p)),
            Err(e) => rval.push(Err(e.change_context(ErrorStack::IndividualError))),
        }
    }

    Ok(rval)
}
