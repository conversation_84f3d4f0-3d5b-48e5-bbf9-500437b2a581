use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AmiIdIsNone(crate::Ami),
    DbError,
    DeserializeAmiGraphkey(String, std::path::PathBuf),
    EtcMediaproctorsExistButNoVideoprocAmi,
    GetMediaproctor(crate::MediaProctor),
    GetMediaproctorAmi(crate::Ami),
    GetTerraformData,
    GetVideoprocAmi,
    NotOneFound(crate::MediaProctor),
    SelectFromDb,
    SelectMediaproctors,
}

#[derive(Debug, Clone)]
pub struct MediaProctor {
    pub graphkey: crate::MediaProctor,
    pub name: String,
    pub secret: String,
    pub ami_graphkey: crate::Ami,
    pub mp_process: MediaProctorProcess,
    pub mp_stream: MediaProctorStream,
    pub security_group_id: Option<String>,
    pub source: std::path::PathBuf,
}

impl MediaProctor {
    pub async fn get_ami_id(&self, app: &ace_db::App) -> error_stack::Result<String, ErrorStack> {
        let ami = crate::ami::get(self.ami_graphkey.clone(), app)
            .await
            .change_context_lazy(|| ErrorStack::GetMediaproctorAmi(self.ami_graphkey.clone()))?;

        Ok(ami.ami_id)
    }
}

impl crate::GraphValueExt for MediaProctor {}

#[derive(Debug, Clone)]
pub struct MediaProctorProcess {
    pub instance_type: String,
    pub timeout: u32,
}

impl crate::GraphValueExt for MediaProctorProcess {}

#[derive(Debug, Clone)]
pub struct MediaProctorStream {
    pub instance_type: String,
    pub timeout: u32,
}

impl crate::GraphValueExt for MediaProctorStream {}

pub async fn get(
    gk_mediaproctor: &crate::MediaProctor,
    app: &ace_db::App,
) -> error_stack::Result<MediaProctor, ErrorStack> {
    let filter = crate::MediaProctorFilter::One(gk_mediaproctor.clone());
    let mut mediaproctors = select_result(&filter, app).await?;

    // Validate that one record was returned
    let mediaproctor: Result<MediaProctor, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if mediaproctors.len() == 1 {
            mediaproctors.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_mediaproctor.clone()))
        }
    };

    mediaproctor.change_context_lazy(|| ErrorStack::GetMediaproctor(gk_mediaproctor.to_owned()))
}

pub async fn select(
    filter: crate::MediaProctorFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<MediaProctor>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for mediaproctor in select_result(&filter, app).await? {
        match mediaproctor {
            Ok(mp) => {
                rval.push(mp);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::MediaProctorFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<MediaProctor, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::MediaProctorFilter::All => ace_db::Filter::All,
        crate::MediaProctorFilter::One(crate::MediaProctor::Db(name)) => {
            ace_db::Filter::One(name.to_owned())
        }
        crate::MediaProctorFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let terraform_output = ace_db::data::terraform_output::get(&app.data_path)
        .await
        .change_context_lazy(|| ErrorStack::GetTerraformData)?;

    // Get mediaproctors
    let mediaproctors = ace_db::mediaproctor::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for mediaproctor in mediaproctors {
        match mediaproctor {
            Ok(mediaproctor) => {
                let mp = construct(
                    mediaproctor,
                    &terraform_output.mediaproctor_security_group_id,
                )
                .await;
                rval.push(mp);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    mediaproctor: ace_db::etc::mediaproctor::MediaProctor,
    mediaproctor_security_group_id: &Option<String>,
) -> error_stack::Result<MediaProctor, ErrorStack> {
    // Use either the specified ami from the toml file or the latest videoproc ami
    let ami_graphkey = match mediaproctor.ami_graphkey {
        Some(gk) => match crate::GraphKeyExt::deserialize(&gk) {
            Ok(g) => g,
            Err(e) => {
                error_stack::bail!(ErrorStack::DeserializeAmiGraphkey(
                    e.to_string(),
                    mediaproctor.path
                ));
            }
        },
        None => crate::Ami::Packer(crate::Packer::Ubuntu2204Videoproc),
    };

    let mp_process = MediaProctorProcess {
        instance_type: mediaproctor.mp_process.instance_type,
        timeout: mediaproctor.mp_process.timeout,
    };

    let mp_stream = MediaProctorStream {
        instance_type: mediaproctor.mp_stream.instance_type,
        timeout: mediaproctor.mp_stream.timeout,
    };

    Ok(MediaProctor {
        graphkey: crate::MediaProctor::Db(mediaproctor.name.clone()),
        name: mediaproctor.name,
        secret: mediaproctor.secret,
        ami_graphkey,
        mp_process,
        mp_stream,
        security_group_id: mediaproctor_security_group_id.to_owned(),
        source: mediaproctor.path,
    })
}
