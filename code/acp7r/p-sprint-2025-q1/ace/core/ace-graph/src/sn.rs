use error_stack::ResultExt;
use ipnetwork::Ipv4Network;
use std::net::Ipv4Addr;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetConfig,
    InvalidConfigCidrBlock(String),
    NotOneSubnetFound(crate::Subnet, usize),
}

#[derive(Debug)]
pub struct Subnet {
    pub graphkey: crate::Subnet,
    pub name: String,
    pub source: String,
    // Other information...
    pub availability_zone: String,
    pub cidr_block: Ipv4Network,
    pub map_public_ip_on_launch: bool,
    pub vpc_id: VpcId,
}

#[derive(Debug)]
pub enum VpcId {
    DeterminedByAws,
    VpcId(String),
}

impl crate::GraphValueExt for Subnet {}

pub async fn get(
    gk_subnet: crate::Subnet,
    app: &ace_db::App,
) -> error_stack::Result<Subnet, ErrorStack> {
    let filter = crate::SubnetFilter::One(gk_subnet.clone());

    let mut subnets = select(filter, app).await?;
    if subnets.len() == 1 {
        return Ok(subnets.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneSubnetFound(gk_subnet, subnets.len()));
}

pub async fn select(
    filter: crate::SubnetFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Subnet>, ErrorStack> {
    let mut rval = Vec::new();

    for subnet in select_result(filter, app).await? {
        match subnet {
            Ok(subnet) => rval.push(subnet),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::SubnetFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Subnet, ErrorStack>>, ErrorStack> {
    let built_in_filter = match filter {
        crate::SubnetFilter::One(gk_subnet) => Some(gk_subnet),
        crate::SubnetFilter::All => None,
        crate::SubnetFilter::None => return Ok(Vec::new()),
    };

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let mut rval = Vec::new();

    rval.extend(hard_coded_subnets(built_in_filter, config));

    Ok(rval)
}

pub fn hard_coded_subnets(
    gk_sn: Option<crate::Subnet>,
    config: crate::config::Config,
) -> Vec<error_stack::Result<Subnet, ErrorStack>> {
    let mut rval = vec![];

    let filtered_builtins = if let Some(gk) = gk_sn {
        vec![gk]
    } else {
        let builtins = vec![
            crate::Subnet::PublicA,
            crate::Subnet::PublicB,
            crate::Subnet::PublicC,
            crate::Subnet::PrivateA,
            crate::Subnet::PrivateB,
            crate::Subnet::PrivateC,
            crate::Subnet::Vpn,
            crate::Subnet::Ace,
            crate::Subnet::Temporal,
        ];

        builtins
    };

    let subnet = match config.cidr_block {
        ipnetwork::IpNetwork::V4(ipv4) => ipv4.ip(),
        ipnetwork::IpNetwork::V6(ipv6) => {
            let err = error_stack::report!(ErrorStack::InvalidConfigCidrBlock(ipv6.to_string()));
            return vec![Err(err)];
        }
    };

    let subnet_octets = subnet.octets();

    for subnet in filtered_builtins {
        let (availability_zone, cidr_block, map_public_ip_on_launch) = match subnet {
            crate::Subnet::PublicA => {
                let az = config.sn_public_a_az.clone();
                let cidr =
                    Ipv4Network::new(Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 0, 0), 22)
                        .unwrap();

                (az, cidr, true)
            }
            crate::Subnet::PublicB => {
                let az = config.sn_public_b_az.clone();
                let cidr =
                    Ipv4Network::new(Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 4, 0), 22)
                        .unwrap();

                (az, cidr, true)
            }
            crate::Subnet::PublicC => {
                let az = config.sn_public_c_az.clone();
                let cidr =
                    Ipv4Network::new(Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 8, 0), 22)
                        .unwrap();

                (az, cidr, true)
            }
            crate::Subnet::PrivateA => {
                let az = config.sn_private_a_az.clone();
                let cidr = Ipv4Network::new(
                    Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 100, 0),
                    22,
                )
                .unwrap();

                (az, cidr, false)
            }
            crate::Subnet::PrivateB => {
                let az = config.sn_private_b_az.clone();
                let cidr = Ipv4Network::new(
                    Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 104, 0),
                    22,
                )
                .unwrap();

                (az, cidr, false)
            }
            crate::Subnet::PrivateC => {
                let az = config.sn_private_c_az.clone();
                let cidr = Ipv4Network::new(
                    Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 108, 0),
                    22,
                )
                .unwrap();

                (az, cidr, false)
            }
            crate::Subnet::Vpn => {
                let az = config.sn_vpn_az.clone();
                let cidr = Ipv4Network::new(
                    Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 252, 0),
                    22,
                )
                .unwrap();

                (az, cidr, true)
            }
            crate::Subnet::Ace => {
                let az = config.sn_ace_az.clone();
                let cidr =
                    Ipv4Network::new(Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 12, 0), 22)
                        .unwrap();

                (az, cidr, true)
            }
            crate::Subnet::Temporal => {
                let az = config.sn_temporal_az.clone();
                let cidr = Ipv4Network::new(
                    Ipv4Addr::new(subnet_octets[0], subnet_octets[1], 248, 0),
                    22,
                )
                .unwrap();

                (az, cidr, true)
            }
        };

        rval.push(Ok(Subnet {
            graphkey: subnet.clone(),
            name: subnet.variant_to_name(),
            source: "built-in".to_string(),
            availability_zone,
            cidr_block,
            map_public_ip_on_launch,
            vpc_id: VpcId::DeterminedByAws,
        }));
    }

    rval
}
