#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneTlsPrivateKeyFound(crate::TlsPrivateKey, usize),
}

#[derive(Debug)]
pub struct TlsPrivateKey {
    pub graphkey: crate::TlsPrivate<PERSON><PERSON>,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for TlsPrivateKey {}

pub async fn get(
    gk_tls_priv_key: crate::TlsPrivateKey,
) -> error_stack::Result<TlsPrivateKey, ErrorStack> {
    let filter = crate::TlsPrivateKeyFilter::One(gk_tls_priv_key.clone());

    let mut tls_priv_keys = select(filter).await?;
    if tls_priv_keys.len() == 1 {
        return Ok(tls_priv_keys.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneTlsPrivateKeyFound(
        gk_tls_priv_key,
        tls_priv_keys.len()
    ));
}

pub async fn select(
    filter: crate::TlsPrivateKeyFilter,
) -> error_stack::Result<Vec<TlsPrivateKey>, ErrorStack> {
    let mut rval = Vec::new();

    for tls_priv_key in select_result(filter).await? {
        match tls_priv_key {
            Ok(tlspk) => rval.push(tlspk),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::TlsPrivateKeyFilter,
) -> error_stack::Result<Vec<error_stack::Result<TlsPrivateKey, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
