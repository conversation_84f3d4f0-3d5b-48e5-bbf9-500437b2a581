use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAwsAccount,
    GetBrdst(crate::Brdst),
    GetBrDstFromDB,
    NotOneFound(crate::Brdst),
    SelectFromDb,
}

#[derive(Debug, <PERSON>lone)]
pub struct BrDst {
    pub graphkey: crate::Brdst,
    pub name: String,
    pub source: Source,
    pub target: Target,
    pub file_source: std::path::PathBuf,
}

impl crate::GraphValueExt for BrDst {}

#[derive(Debug, Clone)]
pub struct Source {
    pub bucket: String,
    pub bucket_arn: String,
    pub account_key: String,
    pub region: String,
    pub aws_account_id: String,
}

impl crate::GraphValueExt for Source {}

#[derive(Debug, Clone)]
pub struct Target {
    pub bucket: String,
    pub bucket_arn: String,
}

impl crate::GraphValueExt for Target {}

pub async fn get(
    gk_brdst: &crate::Brdst,
    app: &ace_db::App,
) -> error_stack::Result<BrDst, ErrorStack> {
    let filter = crate::BrdstFilter::One(gk_brdst.clone());
    let mut brdsts = select_result(&filter, app).await?;

    // Validate that one record was returned
    let brdst: Result<BrDst, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if brdsts.len() == 1 {
            brdsts.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_brdst.clone()))
        }
    };

    brdst.change_context_lazy(|| ErrorStack::GetBrdst(gk_brdst.to_owned()))
}

pub async fn select(
    filter: &crate::BrdstFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<BrDst>, ErrorStack> {
    let mut rval = Vec::new();

    for brdst in select_result(filter, app).await? {
        match brdst {
            Ok(brdst) => {
                rval.push(brdst);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::BrdstFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<BrDst, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::BrdstFilter::All => ace_db::Filter::All,
        crate::BrdstFilter::One(crate::Brdst::Db(name)) => ace_db::Filter::One(name.to_owned()),
        crate::BrdstFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let brdsts = ace_db::brdst::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for brdst in brdsts {
        match brdst {
            Ok(brdst) => {
                let brdst = construct(brdst, app).await;
                rval.push(brdst);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    brdst: ace_db::etc::brdst::BrDst,
    app: &ace_db::App,
) -> error_stack::Result<BrDst, ErrorStack> {
    let desired_account_key = &brdst.source.account_key;
    let aws_account =
        crate::aws_account::get(&crate::AwsAccount::Db(desired_account_key.to_owned()), app)
            .await
            .change_context(ErrorStack::GetAwsAccount)?;

    let source = Source {
        bucket: brdst.source.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brdst.source.bucket),
        account_key: aws_account.account_key.clone(),
        region: brdst.source.region.clone(),
        aws_account_id: aws_account.aws_account_id,
    };

    let target = Target {
        bucket: brdst.target.bucket.clone(),
        bucket_arn: format!("arn:aws:s3:::{}", brdst.target.bucket),
    };

    Ok(BrDst {
        graphkey: crate::Brdst::Db(brdst.name.clone()),
        name: brdst.name.clone(),
        source,
        target,
        file_source: brdst.file_source,
    })
}
