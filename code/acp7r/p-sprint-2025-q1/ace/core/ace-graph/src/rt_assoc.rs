#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneRouteTableRouteAssocFound(crate::RouteTableAssoc, usize),
}

#[derive(Debug)]
pub struct RouteTableAssoc {
    pub graphkey: crate::RouteTableAssoc,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for RouteTableAssoc {}

pub async fn get(
    gk_rt_assoc: crate::RouteTableAssoc,
) -> error_stack::Result<RouteTableAssoc, ErrorStack> {
    let filter = crate::RouteTableAssocFilter::One(gk_rt_assoc.clone());

    let mut rt_assocs = select(filter).await?;
    if rt_assocs.len() == 1 {
        return Ok(rt_assocs.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneRouteTableRouteAssocFound(
        gk_rt_assoc,
        rt_assocs.len()
    ));
}

pub async fn select(
    filter: crate::RouteTableAssocFilter,
) -> error_stack::Result<Vec<RouteTableAssoc>, ErrorStack> {
    let mut rval = Vec::new();

    for rt_assoc in select_result(filter).await? {
        match rt_assoc {
            Ok(rt_a) => rval.push(rt_a),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::RouteTableAssocFilter,
) -> error_stack::Result<Vec<error_stack::Result<RouteTableAssoc, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}
