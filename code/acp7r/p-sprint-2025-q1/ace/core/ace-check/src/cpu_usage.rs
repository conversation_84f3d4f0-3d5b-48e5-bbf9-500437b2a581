use crate::Check;
use crate::{CheckKeyTrait, CheckTrai<PERSON>, CpuUsageCheck};
use async_trait::async_trait;

pub struct CpuUsageInfo {
    pub check_key: CpuUsageCheck,
    pub status: CpuUsageCheckStatus,
}

impl CpuUsageInfo {
    pub fn new(check_key: CpuUsageCheck, status: CpuUsageCheckStatus) -> Self {
        CpuUsageInfo { check_key, status }
    }
}

#[async_trait]
impl CheckTrait for CpuUsageInfo {
    async fn execute(&self, _app: &ace_core::Application) -> crate::CheckOutcome {
        // Placeholder:
        crate::CheckOutcome {
            details: Box::new(CpuUsageInfo::new(
                CpuUsageCheck::Instance(ace_graph::Instance::Ace),
                CpuUsageCheckStatus::Unexecuted,
            )),
        }
    }

    async fn get_subject_string(&self, _app: &ace_core::Application) -> String {
        match &self.check_key {
            CpuUsageCheck::Instance(instance) => instance.to_string(),
        }
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "CpuUsage".to_string()
    }
}

pub enum CpuUsageCheckStatus {
    Error,
    CloseToLimit,
    OverLimit,
    UnderLimit,
    Unexecuted,
}

#[async_trait]
impl CheckKeyTrait for CpuUsageCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(CpuUsageInfo::new(self, CpuUsageCheckStatus::Unexecuted))
    }
}

pub async fn select_checks(ace_db_app: &ace_db::App) -> Vec<Check> {
    let mut rval = vec![];

    let instances = match ace_graph::ins::select(&ace_graph::InstanceFilter::All, ace_db_app).await
    {
        Ok(instances) => instances,
        Err(_e) => return rval,
    };

    for instance in instances {
        rval.push(Check::CpuUsage(CpuUsageCheck::Instance(instance.graphkey)));
    }

    rval
}
