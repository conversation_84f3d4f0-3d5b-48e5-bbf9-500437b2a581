use crate::Check;
use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, VersionChe<PERSON>};
use async_trait::async_trait;
use error_stack::ResultExt;

#[derive(Debug, ace_proc::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub enum ErrorStack {
    CheckAceBinaryVersion(std::path::PathBuf),
    ReadVersionFile(std::path::PathBuf),
    RunVersionCommand,
}

pub struct VersionCheckInfo {
    pub check_key: VersionCheck,
    pub status: VersionCheckStatus,
    pub bin_version: String,
    pub version_file: String,
}

impl VersionCheckInfo {
    pub fn new(
        check_key: VersionCheck,
        status: VersionCheckStatus,
        bin_version: String,
        version_file: String,
    ) -> Self {
        VersionCheckInfo {
            check_key,
            status,
            bin_version,
            version_file,
        }
    }
}

#[async_trait]
impl CheckTrait for VersionCheckInfo {
    async fn execute(&self, app: &ace_core::Application) -> crate::CheckOutcome {
        let version_path = &app.data_version_file_path;
        let version_contents = match tokio::fs::read(version_path)
            .await
            .change_context(ErrorStack::ReadVersionFile(version_path.to_path_buf()))
        {
            Ok(contents) => contents,
            Err(e) => {
                return crate::CheckOutcome {
                    details: Box::new(VersionCheckInfo::new(
                        self.check_key.clone(),
                        VersionCheckStatus::Error(e),
                        "".to_string(),
                        "".to_string(),
                    )),
                };
            }
        };

        let readable_version_file = String::from_utf8_lossy(&version_contents).to_string();

        // Run `/bin/ace version`
        let mut cmd = tokio::process::Command::new(&app.bin_executable_path);
        cmd.arg("version");

        let output = match cmd
            .output()
            .await
            .change_context(ErrorStack::RunVersionCommand)
        {
            Ok(output) => output,
            Err(e) => {
                return crate::CheckOutcome {
                    details: Box::new(VersionCheckInfo::new(
                        self.check_key.clone(),
                        VersionCheckStatus::Error(e.change_context(
                            ErrorStack::CheckAceBinaryVersion(app.bin_executable_path.clone()),
                        )),
                        "".to_string(),
                        readable_version_file,
                    )),
                };
            }
        };

        let check_status = if output.stdout != version_contents {
            VersionCheckStatus::Mismatch
        } else {
            VersionCheckStatus::Match
        };

        let readable_command_output = String::from_utf8_lossy(&output.stdout).to_string();

        crate::CheckOutcome {
            details: Box::new(VersionCheckInfo::new(
                self.check_key.clone(),
                check_status,
                readable_command_output,
                readable_version_file,
            )),
        }
    }

    async fn get_subject_string(&self, app: &ace_core::Application) -> String {
        format!(
            "{}@{}",
            &app.path_accountkey_region.account_key, &app.path_accountkey_region.region
        )
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "Ace Version".to_string()
    }
}

pub enum VersionCheckStatus {
    Match,
    Mismatch,
    Unexecuted,
    Error(error_stack::Report<ErrorStack>),
}

#[async_trait]
impl CheckKeyTrait for VersionCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(VersionCheckInfo::new(
            self,
            VersionCheckStatus::Unexecuted,
            "".to_string(),
            "".to_string(),
        ))
    }
}

pub fn get_check(app: &ace_core::Application) -> Check {
    let acctkey_at_region = format!(
        "{}@{}",
        &app.path_accountkey_region.account_key, &app.path_accountkey_region.region
    );
    Check::Version(VersionCheck::Environment(acctkey_at_region))
}
