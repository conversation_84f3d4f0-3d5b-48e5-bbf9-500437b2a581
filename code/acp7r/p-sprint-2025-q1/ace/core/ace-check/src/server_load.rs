use std::vec;

use crate::Check;
use crate::{<PERSON><PERSON><PERSON><PERSON>rai<PERSON>, Check<PERSON>rait, ServerLoadCheck};
use async_trait::async_trait;

pub struct ServerLoadInfo {
    pub check_key: ServerLoadCheck,
    pub status: ServerLoadCheckStatus,
}

impl ServerLoadInfo {
    pub fn new(check_key: ServerLoadCheck, status: ServerLoadCheckStatus) -> Self {
        ServerLoadInfo { check_key, status }
    }
}

#[async_trait]
impl CheckTrait for ServerLoadInfo {
    async fn execute(&self, _app: &ace_core::Application) -> crate::CheckOutcome {
        // Placeholder:
        crate::CheckOutcome {
            details: Box::new(ServerLoadInfo::new(
                ServerLoadCheck::AceServer,
                ServerLoadCheckStatus::Unexecuted,
            )),
        }
    }

    async fn get_subject_string(&self, app: &ace_core::Application) -> String {
        match &self.check_key {
            ServerLoadCheck::AceServer => {
                let config =
                    match ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
                        .await
                    {
                        Ok(config) => config,
                        Err(_e) => return "Error obtaining config".to_string(),
                    };

                config.ace2_server_name
            }
        }
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "ServerLoad".to_string()
    }
}
pub enum ServerLoadCheckStatus {
    Error,
    CloseToLimit,
    OverLimit,
    UnderLimit,
    Unexecuted,
}

#[async_trait]
impl CheckKeyTrait for ServerLoadCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(ServerLoadInfo::new(self, ServerLoadCheckStatus::Unexecuted))
    }
}

pub fn select_checks() -> Vec<Check> {
    let rval = vec![Check::ServerLoad(ServerLoadCheck::AceServer)];

    rval
}
