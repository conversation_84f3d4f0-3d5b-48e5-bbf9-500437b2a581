use crate::Check;
use crate::CheckKeyTrait;
use crate::CheckTrait;
use crate::NsCheck;
use async_trait::async_trait;

pub struct NsCheckInfo {
    pub check_key: NsCheck,
    pub domain: String,
    pub canonical_entries: Vec<String>,
    pub actual_entries: Vec<String>,
    pub actions_needed: Vec<String>,
    pub status: NsCheckStatus,
}

impl NsCheckInfo {
    pub fn new(check_key: NsCheck, status: NsCheckStatus) -> Self {
        NsCheckInfo {
            check_key,
            domain: "".to_string(),
            canonical_entries: vec![],
            actual_entries: vec![],
            actions_needed: vec![],
            status,
        }
    }
}

#[async_trait]
impl CheckTrait for NsCheckInfo {
    async fn execute(&self, _app: &ace_core::Application) -> crate::CheckOutcome {
        // Put actual Ns check here...

        // Placeholder:
        crate::CheckOutcome {
            details: Box::new(NsCheckInfo::new(
                NsCheck::AceSubdomainPrivate,
                NsCheckStatus::Unexecuted,
            )),
        }
    }

    // Want actual domain returned here...
    async fn get_subject_string(&self, app: &ace_core::Application) -> String {
        let config =
            match ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app).await {
                Ok(config) => config,
                Err(_e) => return "Error obtaining config".to_string(),
            };

        match self.check_key {
            NsCheck::AceSubdomainPrivate => config.private_subdomain_name,
            NsCheck::AceSubdomainPublic => config.public_subdomain_name,
        }
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "Ns".to_string()
    }
}

pub enum NsCheckStatus {
    Error(String),
    Match,
    Mismatch {
        missing_records: Vec<String>,
        extra_records: Vec<String>,
    },
    MissingCanonicalRecords,
    NoActualNsRecordsFound,
    Unexecuted,
}

#[async_trait]
impl CheckKeyTrait for NsCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(NsCheckInfo::new(self, NsCheckStatus::Unexecuted))
    }
}

pub fn select_checks() -> Vec<Check> {
    let rval = vec![
        Check::Ns(NsCheck::AceSubdomainPrivate),
        Check::Ns(NsCheck::AceSubdomainPublic),
    ];

    rval
}
