[package]
name = "ace-check"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ace-core = { path = "../../core/ace-core" }
ace-db = { path = "../../core/ace-db" }
ace-graph = { path = "../../core/ace-graph" }
ace-proc = { path = "../../core/ace-proc" }

async-trait = { workspace = true }
chrono = { workspace = true }
comfy-table = { workspace = true }
error-stack = { workspace = true }
glob = { workspace = true }
reqwest = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
