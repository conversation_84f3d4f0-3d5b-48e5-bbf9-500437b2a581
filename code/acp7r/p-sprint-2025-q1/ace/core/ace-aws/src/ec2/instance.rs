use error_stack::ResultExt;
use std::{collections::HashMap, str::FromStr};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    FailedToGetInstanceID,
    FailedToGetInstances,
    FailedToRunInstance(String),
    InstanceTypeFromStr(String),
}

pub struct ClientWrapper {
    pub client: aws_sdk_ec2::Client,
}

pub async fn open() -> error_stack::Result<ClientWrapper, ErrorStack> {
    ClientWrapper::open().await
}

impl ClientWrapper {
    pub async fn open() -> error_stack::Result<Self, ErrorStack> {
        let config = aws_config::load_from_env().await;
        let client = aws_sdk_ec2::Client::new(&config);
        Ok(Self { client })
    }
}

#[derive(Debug)]
pub struct RunInstance {
    pub name: String,
    pub image_id: String,
    pub instance_type: aws_sdk_ec2::model::InstanceType,
    pub subnet_id: String,
    pub security_group_ids: Vec<String>,
    pub max_count: Option<i32>,
    pub min_count: Option<i32>,
    pub key_name: Option<String>,
    pub user_data: Option<String>,
    pub tags: HashMap<String, String>,
    pub instance_initiated_shutdown_behavior: Option<aws_sdk_ec2::model::ShutdownBehavior>,
    pub volume_size: i32,
}

impl RunInstance {
    pub fn simple1(
        name: &str,
        image_id: &str,
        instance_type: &str,
        subnet_id: &str,
        security_group_ids: Vec<String>,
    ) -> error_stack::Result<Self, ErrorStack> {
        let mut tags = HashMap::new();
        tags.insert("Name".to_string(), name.to_string());

        Ok(Self {
            name: name.to_string(),
            image_id: image_id.to_string(),
            instance_type: aws_sdk_ec2::model::InstanceType::from_str(instance_type)
                .change_context(ErrorStack::InstanceTypeFromStr(instance_type.to_string()))?,
            subnet_id: subnet_id.to_string(),
            security_group_ids,
            max_count: Some(1),
            min_count: Some(1),
            key_name: None,
            user_data: None,
            tags,
            instance_initiated_shutdown_behavior: None,
            volume_size: 20,
        })
    }

    pub fn add_tag(&mut self, key: &str, value: &str) {
        self.tags.insert(key.to_string(), value.to_string());
    }

    pub fn terminate_on_shutdown(&mut self) {
        self.instance_initiated_shutdown_behavior =
            Some(aws_sdk_ec2::model::ShutdownBehavior::Terminate);
    }

    pub fn get_user_data_base64(&self) -> Option<String> {
        self.user_data
            .as_ref()
            .map(|user_data| base64_light::base64_encode(user_data))
    }

    pub async fn send(
        &self,
        client_wrapper: ClientWrapper,
    ) -> error_stack::Result<RunInstanceOutput, ErrorStack> {
        let result = client_wrapper
            .client
            .run_instances()
            .set_image_id(Some(self.image_id.to_string()))
            .set_instance_type(Some(self.instance_type.clone()))
            .set_max_count(self.max_count)
            .set_min_count(self.min_count)
            .set_subnet_id(Some(self.subnet_id.to_string()))
            .set_security_group_ids(Some(self.security_group_ids.clone()))
            .set_key_name(self.key_name.clone())
            .set_user_data(self.get_user_data_base64())
            .set_block_device_mappings(Some(vec![
                aws_sdk_ec2::model::BlockDeviceMapping::builder()
                    .device_name("/dev/sda1".to_string())
                    .ebs(
                        aws_sdk_ec2::model::EbsBlockDevice::builder()
                            .delete_on_termination(true)
                            .volume_size(self.volume_size)
                            .volume_type(aws_sdk_ec2::model::VolumeType::Gp3)
                            .build(),
                    )
                    .build(),
            ]))
            .set_instance_initiated_shutdown_behavior(
                self.instance_initiated_shutdown_behavior.clone(),
            )
            .send()
            .await
            .change_context(ErrorStack::FailedToRunInstance(format!("{:?}", self)))?;

        let instances = match result.instances() {
            Some(instances) => instances,
            None => {
                error_stack::bail!(ErrorStack::FailedToGetInstances)
            }
        };

        let mut tags = vec![];
        for (key, value) in &self.tags {
            tags.push(
                aws_sdk_ec2::model::Tag::builder()
                    .key(key.clone())
                    .value(value.clone())
                    .build(),
            );
        }

        let mut instance_ids = vec![];
        let mut errors = vec![];

        for instance in instances {
            let instance_id = match instance.instance_id() {
                Some(instance_id) => instance_id,
                None => {
                    error_stack::bail!(ErrorStack::FailedToGetInstanceID)
                }
            };

            instance_ids.push(instance_id.to_owned());

            // Try up to 5 times to apply the tags
            for i in 0..5 {
                let resp = client_wrapper
                    .client
                    .create_tags()
                    .resources(instance_id)
                    .set_tags(Some(tags.clone()))
                    .send()
                    .await;

                match resp {
                    Ok(_) => break,
                    Err(e) => {
                        errors.push(format!(
                            "Error {} tagging instance {}: {}",
                            i, instance_id, e
                        ));
                        tokio::time::sleep(std::time::Duration::from_secs(i)).await;
                    }
                }
            }
        }

        Ok(RunInstanceOutput {
            instance_ids,
            errors,
        })
    }
}

pub struct RunInstanceOutput {
    pub instance_ids: Vec<String>,
    pub errors: Vec<String>,
}
