[package]
name = "ace-aws"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ace-proc = { path = "../../core/ace-proc" }

anyhow = { workspace = true }
aws-config = { workspace = true }
aws-sdk-ec2 = { workspace = true }
aws-sdk-route53 = { workspace = true }
base64_light = {workspace = true }
error-stack = { workspace = true }
ipnetwork = { workspace = true }
tokio = { workspace = true }
serde_json = { workspace = true }
