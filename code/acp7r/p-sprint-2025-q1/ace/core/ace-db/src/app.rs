use super::etc::app::EtcApp;
use crate::ErrorStack;
use error_stack::ResultExt;

pub type AccountKey = String;
pub type AwsAccountId = String;

/// This function returns a merged list of apps from etc and an external database
/// (actual data collection from database not implemented yet).
pub async fn select_result(
    filter_name: crate::Filter<&str>,
    ace_db_app: &crate::App,
) -> crate::SelectResult<EtcApp, ErrorStack> {
    let mut rval = vec![];

    // Apps from /etc/
    let etc_apps = crate::etc::app::select(filter_name, ace_db_app)
        .await
        .change_context(ErrorStack::SelectEtcApps)?;

    for app in etc_apps.into_values() {
        match app {
            Ok(app) => {
                rval.push(validate_public_read(app));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectEtcApps)));
            }
        }
    }

    // Insert database selection here

    Ok(rval)
}

fn validate_public_read(app: EtcApp) -> error_stack::Result<EtcApp, ErrorStack> {
    let mut errs = vec![];

    for bucket in app.bucket_map.values() {
        for pr in &bucket.public_read_paths {
            if !pr.starts_with('/') {
                errs.push(error_stack::report!(
                    ErrorStack::AppBucketPublicKeyDoesNotStartWithSlash(pr.clone())
                ));
            }
        }
    }

    if !errs.is_empty() {
        let mut report = error_stack::Report::new(ErrorStack::ValidateAppBucketPublicRead);
        report.extend(errs);

        Err(report)
    } else {
        Ok(app)
    }
}
