use super::etc::account::aws_account::AccountRegion;
use crate::ErrorStack;
use error_stack::ResultExt;
use regex::Regex;
use std::collections::{HashMap, HashSet};

#[derive(Debug, Clone)]
pub struct AwsAccount {
    pub account_key: AccountKey,
    pub aws_account_id: AwsAccountId,
    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,
    pub region: HashMap<String, AccountRegion>,
    pub source: std::path::PathBuf,
}

pub type AccountKey = String;
pub type AwsAccountId = String;

/// This function returns a list of accounts from account.toml.  (In future, it will consult the postgres database).
/// Merging logic currently is:
/// - Region information is merged if is only in one or the other.
/// - Region information in both locations is accepted only if it is the same.
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectR<PERSON>ult<AwsAccount, ErrorStack> {
    let mut rval = vec![];
    let mut final_account_map: HashMap<String, error_stack::Result<AwsAccount, ErrorStack>> =
        HashMap::new();

    // Users from account.toml
    let mut account_toml_accounts =
        crate::etc::account::aws_account::select(filter_name, &app.etc_account_file_path)
            .await
            .change_context(ErrorStack::SelectUsersFromAccountToml)?;

    // Insert database selection function/code here
    let mut external_db_accounts: HashMap<
        AccountKey,
        Result<AwsAccount, error_stack::Report<ErrorStack>>,
    > = HashMap::new();

    let unique_accounts = account_toml_accounts
        .keys()
        .chain(external_db_accounts.keys())
        .cloned()
        .collect::<HashSet<_>>();

    for account in unique_accounts.into_iter() {
        let account_toml_account = account_toml_accounts.remove(&account);
        let corresponding_external_db_account = external_db_accounts.remove(&account);

        let (account_key, account_result) =
            match (account_toml_account, corresponding_external_db_account) {
                // Valid account information found both places, merge
                (Some(Ok(toml_account)), Some(Ok(external_account))) => {
                    let mut merged_sysadmins: HashSet<String> =
                        toml_account.sysadmins.iter().cloned().collect();
                    for sysadmin in external_account.sysadmins {
                        if !merged_sysadmins.contains(&sysadmin) {
                            merged_sysadmins.insert(sysadmin);
                        }
                    }
                    let merged_sysadmins_vec = merged_sysadmins
                        .into_iter()
                        .map(|x| x.to_string())
                        .collect();

                    let mut merged_regions = toml_account.region.clone();
                    for region in external_account.region.keys() {
                        if !merged_regions.contains_key(region) {
                            // If only one or the other has a region, merge them.  Only raise error if both have region config information AND they do not match!
                            match (
                                toml_account.region.get(region),
                                external_account.region.get(region),
                            ) {
                                (Some(toml_region), Some(external_region)) => {
                                    if toml_region != external_region {
                                        return Err(error_stack::report!(
                                            ErrorStack::AccountRegionMismatch(
                                                toml_region.clone(),
                                                external_region.clone()
                                            )
                                        ));
                                    }
                                }
                                (Some(toml_region), None) => {
                                    merged_regions.insert(region.to_string(), toml_region.clone());
                                }
                                (None, Some(external_region)) => {
                                    merged_regions
                                        .insert(region.to_string(), external_region.clone());
                                }
                                (None, None) => {
                                    continue;
                                }
                            }
                        }
                    }

                    // Check that the aws account ID is the same.
                    if toml_account.aws_account_id != external_account.aws_account_id {
                        return Err(ErrorStack::AwsAccountIdMismatch(
                            toml_account.account_key,
                            toml_account.aws_account_id,
                            external_account.aws_account_id,
                        )
                        .into());
                    }

                    let merged_account = AwsAccount {
                        account_key: account.clone(),
                        aws_account_id: toml_account.aws_account_id,
                        public_domain: toml_account.public_domain,
                        private_domain: toml_account.private_domain,
                        sysadmins: merged_sysadmins_vec,
                        region: merged_regions,
                        source: toml_account.source,
                    };

                    (account, Ok(merged_account))
                }

                // Valid account information only found in account.toml
                (Some(Ok(toml_account)), None) => {
                    let a = AwsAccount {
                        account_key: toml_account.account_key,
                        aws_account_id: toml_account.aws_account_id,
                        public_domain: toml_account.public_domain,
                        private_domain: toml_account.private_domain,
                        sysadmins: toml_account.sysadmins,
                        region: toml_account.region,
                        source: toml_account.source,
                    };

                    (account, Ok(a))
                }

                // Valid account information found only in external database
                (None, Some(Ok(external_account))) => {
                    let a = AwsAccount {
                        account_key: external_account.account_key,
                        aws_account_id: external_account.aws_account_id,
                        public_domain: external_account.public_domain,
                        private_domain: external_account.private_domain,
                        sysadmins: external_account.sysadmins,
                        region: external_account.region,
                        source: external_account.source,
                    };

                    (account, Ok(a))
                }

                // Error only found in either account.toml or external account - no corresponding account
                (Some(Err(e)), None) | (None, Some(Err(e))) => (account, Err(e)),

                // Errors found in BOTH sources.  Update errorstack to include both errors.
                (Some(Err(mut e1)), Some(Err(e2))) => {
                    e1.extend_one(e2);

                    (account, Err(e1))
                }

                // Error only found in account.toml:
                (Some(Err(e)), Some(Ok(_))) => (account, Err(e)),

                // Error only found in external account:
                (Some(Ok(_)), Some(Err(e))) => (account, Err(e)),

                // Not technically possible to reach, here to satisfy the Rust compiler
                (None, None) => {
                    continue;
                }
            };

        // Insert information into final_account_map - But SORT everything first AND VALIDATE
        if let Ok(mut account) = account_result {
            account.sysadmins.sort();

            let mut errs = vec![];

            let valid_account_id = validate_account_id(&account.aws_account_id);
            let valid_account_regions = validate_account_regions(&account, &account_key);

            if let Err(e) = valid_account_id {
                errs.push(e);
            }
            if let Err(e) = valid_account_regions {
                errs.push(e);
            }

            let account_result = if errs.is_empty() {
                Ok(account)
            } else {
                let mut report =
                    error_stack::Report::new(ErrorStack::ValidateAccount(account_key.clone()));
                report.extend(errs);

                Err(report)
            };

            final_account_map.insert(account_key, account_result);
        } else {
            final_account_map.insert(account_key, account_result);
        }
    }

    rval.extend(final_account_map.into_values());

    Ok(rval)
}

fn validate_account_id(account_id: &str) -> error_stack::Result<(), ErrorStack> {
    let re = Regex::new(r"^[0-9]{12}$").unwrap();
    if re.is_match(account_id) {
        Ok(())
    } else {
        error_stack::bail!(ErrorStack::InvalidAccountID(account_id.to_string()));
    }
}

fn validate_account_regions(
    account: &AwsAccount,
    account_key: &AccountKey,
) -> error_stack::Result<(), ErrorStack> {
    for (region_name, region) in &account.region {
        if region.cidr_block.prefix() != 16 && region.ace2 {
            let e = format!(
                "[account.{account_key}.region.{region_name}].subnet must be a /16 network"
            );

            error_stack::bail!(ErrorStack::InvalidSubnet(e));
        }
    }

    Ok(())
}
