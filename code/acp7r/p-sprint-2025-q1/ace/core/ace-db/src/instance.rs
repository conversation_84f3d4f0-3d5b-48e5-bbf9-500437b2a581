use crate::ErrorStack;
use crate::etc::instance::Instance;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Instance, ErrorStack> {
    let mut rval = vec![];

    let etc_instances = crate::etc::instance::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcInstances)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_instances.into_values());

    Ok(rval)
}
