use crate::ErrorStack;
use crate::local_asset::Asset;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter: crate::local_asset::AssetFilter,
    app: &crate::App,
) -> crate::SelectResult<Asset, ErrorStack> {
    let mut rval: Vec<Result<Asset, error_stack::Report<ErrorStack>>> = vec![];

    let local_assets = crate::local_asset::select_result(filter, app)
        .await
        .change_context(ErrorStack::SelectEtcAssets)?;

    // TODO: Insert external database
    // ...

    rval.extend(local_assets.into_values());

    Ok(rval)
}
