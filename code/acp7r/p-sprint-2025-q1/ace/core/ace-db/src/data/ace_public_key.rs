use crate::ErrorStack;
use crate::etc::keypair::KeyPair;
use error_stack::ResultExt;

/// # Errors
///
/// Bails lazily under the following circumstances:
/// - If the file private ace key file cannot be read.
pub async fn get_public_ace_key(
    ace_db_app: &crate::App,
) -> error_stack::Result<Option<KeyPair>, ErrorStack> {
    let path = &ace_db_app.ace_public_key_path;

    if ace_db_app.data_path.exists() {
        let content = tokio::fs::read_to_string(path.clone())
            .await
            .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

        return Ok(Some(KeyPair {
            name: "ace".to_string(),
            public_key: content,
            source: path.to_path_buf(),
        }));
    }

    Ok(None)
}
