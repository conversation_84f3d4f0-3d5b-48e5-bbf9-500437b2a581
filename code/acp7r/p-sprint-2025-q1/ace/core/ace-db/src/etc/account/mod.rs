#![allow(clippy::module_inception)]
pub mod aws_account;
pub mod peercon;
pub mod user;

use std::path::Path;

use crate::ErrorStack;
use error_stack::ResultExt;
use garbage::CNSL;

pub type AccountFileToml = toml::value::Table;

#[rustfmt::skip]
/// # Errors
/// 
/// Returns an error if the call to `current_account_key_and_region()` fails.
pub fn example_string() -> error_stack::Result<String, ErrorStack> {
    let env_info = crate::current_account_key_and_region().change_context(ErrorStack::GetAccountKeyAndRegion)?;
    
    Ok(CNSL!(r#"
        # General settings
        git_repo = "**************:yourorg/yourrepo.git"
        
        # Users should be listed
        [user.yourname]
        ssh_keys = []
        static_networks = []
        
        # A mapping of account key -> aws account id
        [account."#, env_info.account_key, r#"]
        aws_account_id = "xxxxxxxxxxxx"

        [account."#, env_info.account_key, ".region.", env_info.region, r#"]
        cidr_block = "10.xx.0.0/16"
        # vpc_id = "xxxxxxxxxxxx"
        ace2 = true

    "#))
}

async fn parse_account_file(
    etc_account_file_path: &Path,
) -> error_stack::Result<AccountFileToml, ErrorStack> {
    let content = tokio::fs::read_to_string(etc_account_file_path)
        .await
        .change_context_lazy(|| ErrorStack::ReadAccountFile(etc_account_file_path.to_owned()))?;

    let account_file: toml::map::Map<String, toml::Value> =
        toml::value::Table::from(toml::from_str(&content).change_context_lazy(|| {
            ErrorStack::ParseAccountFile(etc_account_file_path.to_owned())
        })?);

    // check spelling of each key entry
    let canonical_key_values = ["account", "git_repo", "peercon", "user"];

    for key in account_file.keys() {
        if !canonical_key_values.contains(&key.as_str()) {
            let e = format!("{}:\n{key}", etc_account_file_path.display(),);

            error_stack::bail!(ErrorStack::InvalidKey(e));
        }
    }
    Ok(account_file)
}
