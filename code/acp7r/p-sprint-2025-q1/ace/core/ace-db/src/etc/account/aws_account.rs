use crate::ErrorStack;
use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use serde::Deserialize;
use serde_with::serde_as;
use std::{collections::HashMap, path::Path};

pub type AccountKey = String;
pub type AwsAccountId = String;

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct SerdeAwsAccount {
    pub aws_account_id: AwsAccountId,

    pub public_domain: String,
    pub private_domain: String,
    #[serde(default)]
    pub sysadmins: Vec<String>,

    // add a default for this field
    #[serde(default)]
    pub region: HashMap<String, SerdeAwsAccountRegion>,
}

#[serde_as]
#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct SerdeAwsAccountRegion {
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

#[derive(Debug, Clone)]
pub struct AwsAccount {
    pub account_key: String,
    pub aws_account_id: AwsAccountId,
    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,
    pub region: HashMap<String, AccountRegion>,
    pub source: std::path::PathBuf,
}

#[derive(Debug, Clone, PartialEq)]
pub struct AccountRegion {
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

/// # Errors
///
/// Bails only if `get_account_map()` bails.
pub(crate) async fn select(
    filter_account_key: crate::Filter<String>,
    etc_account_file_path: &Path,
) -> crate::SelectHashMapResult<AwsAccount, ErrorStack> {
    if matches!(filter_account_key, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let mut rval = HashMap::new();

    let aws_account_map = get_account_map(etc_account_file_path)
        .await
        .change_context(ErrorStack::GetAccountMap)?;

    for (account_key, aws_account) in aws_account_map {
        // Handle filtering
        if !match &filter_account_key {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => account_key == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&account_key),
            crate::Filter::None => false,
        } {
            continue;
        }

        rval.insert(
            account_key.clone(),
            process_account(&account_key, &aws_account, etc_account_file_path),
        );
    }

    Ok(rval)
}

/// # Errors
///
/// Bails under the following circumstances:
/// - If the account table is not found in the account file.
/// - If the account table cannot be un-flattened.
/// - If the account table cannot be converted into a `HashMap`.
pub async fn get_account_map(
    etc_account_file_path: &Path,
) -> error_stack::Result<HashMap<String, SerdeAwsAccount>, ErrorStack> {
    let account_toml = super::parse_account_file(etc_account_file_path).await?;

    let Some(flat_account_table) = account_toml.get("account") else {
        error_stack::bail!(ErrorStack::NoAccountTableFound(
            etc_account_file_path.to_path_buf()
        ));
    };

    let Some(account_table) = flat_account_table.as_table() else {
        error_stack::bail!(ErrorStack::UnflattenAccountTable(
            etc_account_file_path.to_path_buf()
        ));
    };

    let account_map: HashMap<String, SerdeAwsAccount> = account_table
        .clone()
        .try_into()
        .change_context(ErrorStack::ParseAccountHashMap(
            etc_account_file_path.to_path_buf(),
        ))?;

    Ok(account_map)
}

fn process_account(
    account_key: &str,
    account: &SerdeAwsAccount,
    etc_account_file_path: &Path,
) -> error_stack::Result<AwsAccount, ErrorStack> {
    let region_map = account
        .region
        .iter()
        .map(|(region_name, region)| {
            (
                region_name.clone(),
                AccountRegion {
                    vpc_id: region.vpc_id.clone(),
                    cidr_block: region.cidr_block,
                    ace2: region.ace2,
                },
            )
        })
        .collect();

    let account = AwsAccount {
        account_key: account_key.to_string(),
        aws_account_id: account.aws_account_id.clone(),
        public_domain: account.public_domain.clone(),
        private_domain: account.private_domain.clone(),
        sysadmins: account.sysadmins.clone(),
        region: region_map,
        source: etc_account_file_path.to_path_buf(),
    };

    Ok(account)
}
