use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Domain {
    pub name: String,
    pub aws: AwsConfig,

    #[serde(default)]
    pub subdomain: Vec<Subdomain>,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct AwsConfig {
    pub hosted_zone_id: String,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Subdomain {
    pub name: String,
    pub tls: bool,
    pub wildcard: bool,
    pub cname: Option<String>,
}

/// Selects domains from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<Domain, ErrorStack> {
    // Use glob to get all the file names, then iterate and call parse on each one
    // etc/domain.*.toml

    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/domain.*.toml", &ace_db_app.etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let domain_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => domain_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&domain_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let domain_result = parse_file(&domain_name, &file).await;
        rval.insert(domain_name, domain_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/domain.name.toml
    // return the name part of the file name

    let re = Regex::new(r"^domain\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &std::path::Path) -> error_stack::Result<Domain, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut domain: Domain =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    // name is the name part of the file name, and in this case, it is the domain name without dots.
    let domain_without_dots = domain.name.replace('.', "-");
    let suffix = format!(".{}", domain.name);

    if name != domain_without_dots {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            domain_without_dots.to_string(),
            path.to_path_buf()
        ));
    }

    for subdomain in &domain.subdomain {
        if !subdomain.name.ends_with(&suffix) {
            error_stack::bail!(ErrorStack::DomainInvalidSubdomainName(format!(
                "Subdomain `{}` does not end with domain name `{}`",
                subdomain.name, domain.name
            )));
        }
    }

    domain.source = path.to_path_buf();

    Ok(domain)
}
