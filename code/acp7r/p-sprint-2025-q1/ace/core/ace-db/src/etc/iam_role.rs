use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct RawIamRole {
    pub name: String,
    pub role_type: RawIamRoleType,

    #[serde(default = "default_with_instance_profile")]
    pub with_instance_profile: bool,

    #[serde(default)]
    pub source: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
#[serde(untagged)]
pub enum RawIamRoleType {
    Existing {
        iam_role_name: String,
    },
    /// This is meant to have a string that is in JSON format - validation is done in `parse_file`
    New {
        policy: String,
    },
}

#[derive(Debug, Deserialize)]
pub struct IamRole {
    pub name: String,
    pub role_type: IamRoleType,
    pub with_instance_profile: bool,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
pub enum IamRoleType {
    Existing { iam_role_name: String },
    New { policy: serde_json::Value },
}

fn default_with_instance_profile() -> bool {
    false
}

/// Selects iam roles from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<IamRole, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/iam-role.*.toml", &ace_db_app.etc_path.to_string_lossy());
    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(crate::ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let iamrole_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => iamrole_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&iamrole_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let iamrole_result = parse_file(&iamrole_name, &file).await;
        rval.insert(iamrole_name, iamrole_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    let re = Regex::new(r"^iam-role\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<IamRole, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let rawiamrole: RawIamRole =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != rawiamrole.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            rawiamrole.name,
            path.to_path_buf()
        ));
    }

    // Validate the JSON
    // *HOWEVER* it is up to the User it is the **CORRECT** JSON
    let role_type = match rawiamrole.role_type {
        RawIamRoleType::Existing { iam_role_name } => IamRoleType::Existing { iam_role_name },
        RawIamRoleType::New { policy } => {
            let policy: serde_json::Value = serde_json::from_str(&policy)
                .change_context(ErrorStack::IamRoleInvalidJSON(policy.clone()))?;

            IamRoleType::New { policy }
        }
    };

    Ok(IamRole {
        name: rawiamrole.name,
        role_type,
        with_instance_profile: rawiamrole.with_instance_profile,
        source: path.to_path_buf(),
    })
}
