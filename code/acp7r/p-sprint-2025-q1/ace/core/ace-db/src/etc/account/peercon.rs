use crate::ErrorStack;
use serde::Deserialize;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct SerdePeeringConnection {
    pub description: String,
    pub peering_connection_id: String,
    pub peer1: SerdePeeringConnectionPeer,
    pub peer2: SerdePeeringConnectionPeer,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct SerdePeeringConnectionPeer {
    pub account_key: String,
    pub region: String,
}

pub(crate) async fn select_result(
    filter: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> Vec<error_stack::Result<SerdePeeringConnection, ErrorStack>> {
    get_peercon_list(filter, ace_db_app).await
}

/// Does not return a HashMap because we don't know the name ahead of time via the filesystem.
/// - To get an identification, we must successfully deserialize.
async fn get_peercon_list(
    filter: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> Vec<error_stack::Result<SerdePeeringConnection, ErrorStack>> {
    if matches!(filter, crate::Filter::None) {
        return Vec::new();
    };

    let mut rval = Vec::new();

    let account_toml = match super::parse_account_file(&ace_db_app.etc_account_file_path).await {
        Ok(account_toml) => account_toml,
        Err(e) => {
            return vec![Err(e)];
        }
    };

    let Some(flat_peercon_list) = account_toml.get("peercon") else {
        // Not an error if the peercon section is empty/doesn't exist.
        return Vec::new();
    };

    match flat_peercon_list.as_array() {
        Some(peer_list) => {
            for peer in peer_list {
                // Can't pre-emptively filter because we need to deserialize the Peercon to check it.
                let peercon: SerdePeeringConnection = match peer.clone().try_into() {
                    Ok(peercon) => peercon,
                    Err(e) => {
                        return vec![Err(ErrorStack::ParsePeercon(e).into())];
                    }
                };

                match &filter {
                    crate::Filter::All => (),
                    crate::Filter::One(filter_name) => {
                        if peercon.peering_connection_id != *filter_name {
                            continue;
                        }
                    }
                    crate::Filter::Many(filter_names) => {
                        if !filter_names.contains(&peercon.peering_connection_id) {
                            continue;
                        }
                    }
                    crate::Filter::None => {
                        continue;
                    }
                }

                rval.push(Ok(peercon));
            }
        }
        None => {
            return vec![Err(ErrorStack::AccountFilePeerconListNotArray(
                ace_db_app.etc_account_file_path.to_path_buf(),
            )
            .into())];
        }
    };

    rval
}
