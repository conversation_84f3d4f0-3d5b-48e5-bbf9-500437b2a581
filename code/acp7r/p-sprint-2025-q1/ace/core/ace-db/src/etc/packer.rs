use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Packer {
    pub name: String,
    pub instance_type: String,
    pub ssh_username: String,
    pub most_recent: bool,
    pub owners: Vec<String>,
    pub bash_text: String,
    pub filters: HashMap<String, String>,

    #[serde(default)]
    pub provisioners: Vec<SerdeProvisioner>,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct SerdeProvisioner {
    pub rel_source: std::path::PathBuf,
    pub abs_dest: std::path::PathBuf,
}

/// Selects packers from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<Packer, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/packer.*.toml", &ace_db_app.etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(crate::ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let packer_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => packer_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&packer_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let packer_result = parse_file(&packer_name, &file).await;
        rval.insert(packer_name, packer_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/packer.name.toml

    let re = Regex::new(r"^packer\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<Packer, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut packer: Packer =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    // Check that the name in the file matches packer.{name}.toml
    if name != packer.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            packer.name,
            path.to_owned()
        ));
    }

    packer.source = path.to_owned();

    // Make sure that owners is not empty
    if packer.owners.is_empty() {
        error_stack::bail!(ErrorStack::EtcPackerOwnersEmpty(path.to_owned()));
    }

    // Make sure that filters is not empty
    if packer.filters.is_empty() {
        error_stack::bail!(ErrorStack::EtcPackerFiltersEmpty(path.to_owned()));
    }

    Ok(packer)
}
