use crate::ErrorStack;
use crate::etc::ecr::EcrRepo;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<EcrRepo, ErrorStack> {
    let mut rval = vec![];

    let etc_private_ecrs = crate::etc::ecr::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcEcrs)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_private_ecrs.into_values());

    Ok(rval)
}
