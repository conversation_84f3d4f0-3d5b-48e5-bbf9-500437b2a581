use crate::ErrorStack;
use crate::etc::domain::Domain;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Domain, ErrorStack> {
    let mut rval = vec![];

    let etc_dockers = crate::etc::domain::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcDomains)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_dockers.into_values());

    Ok(rval)
}
