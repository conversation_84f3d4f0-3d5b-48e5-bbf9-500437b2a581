use crate::Error<PERSON>tack;
use crate::etc::iam_role::IamRole;
use error_stack::ResultExt;

/// TODO: Add external database collection, determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<IamRole, ErrorStack> {
    let mut rval = vec![];

    // IamRoles from etc
    let etc_iam_roles = crate::etc::iam_role::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcIamRoles)?;

    // TODO: Insert external database iamroles here
    // ...

    rval.extend(etc_iam_roles.into_values());

    Ok(rval)
}
