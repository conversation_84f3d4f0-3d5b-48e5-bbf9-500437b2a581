use crate::Error<PERSON>tack;
use crate::etc::docker::Docker;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Docker, ErrorStack> {
    let mut rval = vec![];

    let etc_dockers = crate::etc::docker::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcDockers)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_dockers.into_values());

    Ok(rval)
}
