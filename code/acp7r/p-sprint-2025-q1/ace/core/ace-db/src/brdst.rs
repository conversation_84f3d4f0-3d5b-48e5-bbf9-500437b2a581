use crate::ErrorStack;
use crate::etc::brdst::BrDst;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<BrDst, ErrorStack> {
    let mut rval = vec![];

    let etc_brdsts = crate::etc::brdst::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcBrDsts)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_brdsts.into_values());

    Ok(rval)
}
