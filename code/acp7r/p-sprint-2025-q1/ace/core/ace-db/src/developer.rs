use crate::<PERSON><PERSON><PERSON><PERSON>tack;
use crate::etc::developer::Developer;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Developer, ErrorStack> {
    let mut rval = vec![];

    let etc_developers = crate::etc::developer::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcDevelopers)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_developers.into_values());

    Ok(rval)
}
