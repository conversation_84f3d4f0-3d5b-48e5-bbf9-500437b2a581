use crate::Error<PERSON>tack;
use crate::etc::account::peercon::SerdePeeringConnection;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<SerdePeeringConnection, ErrorStack> {
    let mut rval = vec![];

    let etc_peercons = crate::etc::account::peercon::select_result(filter_name, app).await;

    // TODO: Insert external database
    // ...

    rval.extend(etc_peercons);

    Ok(rval)
}
