use error_stack::ResultExt;
use garbage::CNSL;
use std::path::PathBuf;
use tokio::process::Command;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetConfig,
    GetCrl,
    GetInlineServerConfig,
    LoadConfigText,
    NoVpnConfigFound,
    ReadInlineTlsAuthFile,
    ValidateCRLCommandStatus(String),
    VpnConfigNotFound,
    WriteServerConfig,

    ValidateCommandStatus(String),
    InlineTlsFileDoesNotExist(PathBuf),
    CommandStatus(std::io::Error),
    ExitStatus(std::process::ExitStatus),
}

pub async fn create(app: &crate::Application) -> error_stack::Result<PathBuf, ErrorStack> {
    let (vpn_client_subnet, subnet, vpn) = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;
        (config.vpn_client_cidr_block, config.cidr_block, config.vpn)
    };

    let Some(vpn) = vpn else {
        error_stack::bail!(ErrorStack::VpnConfigNotFound)
    };

    // Add the route for this subnet
    let mut push_lines = vec![format!(
        "push \"route {} {}\"",
        &subnet.ip(),
        &subnet.mask()
    )];

    // Add the route for any additional subnets
    for additional_subnet in &vpn.additional_routed_subnets {
        push_lines.push(format!(
            "push \"route {} {}\"",
            additional_subnet.ip(),
            additional_subnet.mask()
        ));
    }

    // Regenerate the CRL
    let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
    cmd.current_dir(&app.ca_path);
    cmd.arg("gen-crl");
    cmd.env("EASYRSA_BATCH", "1");
    cmd.env("EASYRSA_SILENT", "1");
    cmd.env("EASYRSA_CRL_DAYS", "365");

    validate_status(cmd.status().await)
        .change_context(ErrorStack::ValidateCRLCommandStatus(format!("{:?}", &cmd)))?;

    // Read the new CRL (get it in string form so we can write it to the server config)
    let crl = crate::ca::get_crl(app)
        .await
        .change_context(ErrorStack::GetCrl)?;

    let inline_server_config = get_inline_server_config(app)
        .await
        .change_context(ErrorStack::GetInlineServerConfig)?;

    let server_line = format!(
        "server {} {}",
        vpn_client_subnet.ip(),
        vpn_client_subnet.mask()
    );

    #[rustfmt::skip]
    let rval = CNSL!(r"
        port 1194
        proto udp
        dev tun
        
        # Must be out of the VPN address space
        ", server_line, r"
        
        # Push the route to the clients
        ", push_lines.join("\n"), r"
        
        ifconfig-pool-persist ipp.txt
        keepalive 10 120
        persist-key
        persist-tun
        status server-status.log
        verb 3
        
        auth sha256
        
        <crl-verify>
        ", crl, r"
        </crl-verify>

        ", inline_server_config, r"                    
    ");

    let server_config_path = app.ace_db_app.vpn_path.join("server.conf");
    tokio::fs::write(&server_config_path, &rval)
        .await
        .change_context(ErrorStack::WriteServerConfig)?;

    Ok(server_config_path)
}

pub async fn load_config_text(app: &crate::Application) -> error_stack::Result<String, ErrorStack> {
    let config_path = app.ace_db_app.vpn_path.join("server.conf");
    let config_text = tokio::fs::read_to_string(&config_path)
        .await
        .change_context(ErrorStack::LoadConfigText)?;
    Ok(config_text)
}

pub async fn get_inline_server_config(
    app: &crate::Application,
) -> error_stack::Result<String, ErrorStack> {
    let vpn = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?
        .vpn;

    let Some(vpn_config) = vpn else {
        error_stack::bail!(ErrorStack::NoVpnConfigFound)
    };

    let inline_path = app.ca_path.join(format!(
        "pki/easytls/{}.inline",
        &vpn_config.public_server_name
    ));

    if !inline_path.exists() {
        // Generate it instead of bailing.
        let public_server_name = vpn_config.public_server_name;

        // build server full (part 1)
        {
            let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
            cmd.current_dir(&app.ca_path);
            cmd.arg("build-server-full");
            cmd.arg(&public_server_name);
            cmd.arg("nopass");

            validate_status(cmd.status().await)
                .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
        }

        // build server full (part 2)
        {
            let mut cmd = Command::new(&app.easytls_bin_path);
            cmd.current_dir(&app.ca_path);
            cmd.arg("build-tls-crypt-v2-server");
            cmd.arg(&public_server_name);

            validate_status(cmd.status().await)
                .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
        }

        // Create inline file for server
        let inline_path = app
            .ca_path
            .join(format!("pki/easytls/{}.inline", &public_server_name));

        {
            let mut cmd = Command::new(&app.easytls_bin_path);
            cmd.current_dir(&app.ca_path);
            cmd.arg("inline-tls-auth");
            cmd.arg(&public_server_name);
            cmd.env("EASYTLS_BATCH", "1");
            cmd.env("EASYTLS_SILENT", "1");

            validate_status(cmd.status().await)?;
        }

        if !inline_path.exists() {
            error_stack::bail!(ErrorStack::InlineTlsFileDoesNotExist(inline_path))
        }
    }

    let inline_tls_auth = tokio::fs::read_to_string(inline_path)
        .await
        .change_context(ErrorStack::ReadInlineTlsAuthFile)?;

    Ok(inline_tls_auth)
}

pub fn validate_status(
    status: std::io::Result<std::process::ExitStatus>,
) -> error_stack::Result<(), ErrorStack> {
    match status {
        Ok(status) => {
            if !status.success() {
                error_stack::bail!(ErrorStack::ExitStatus(status))
            }
        }
        Err(e) => {
            error_stack::bail!(ErrorStack::CommandStatus(e))
        }
    }
    Ok(())
}
