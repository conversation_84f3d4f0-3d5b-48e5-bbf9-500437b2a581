use error_stack::ResultExt;
use garbage::CNSL;
use std::path::PathBuf;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CreateInlineTlsAuth,
    CreateSymlink,
    GetConfig,
    GetConfigFilename,
    RemoveClientConfig,
    RemoveSymlink,
    RevokeClient,
    VpnNotConfigured,
    WriteConfig,
}

pub async fn create(
    app: &crate::Application,
    username: &str,
) -> error_stack::Result<PathBuf, ErrorStack> {
    let vpn = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;
        config.vpn
    };

    let Some(vpn_config) = vpn else {
        error_stack::bail!(ErrorStack::VpnNotConfigured)
    };

    let path = get_ovpn_path(app, username).await?;

    println!("path: {:?}", path);

    let inline_tls_auth = crate::ca::create_client(app, username)
        .await
        .change_context(ErrorStack::CreateInlineTlsAuth)?;

    #[rustfmt::skip]
    let rval = CNSL!(r"
        # This file is generated by `ace vpn create-client ", username, r"`
        # on: ", app.hostname, r"
        # at: ", path.display().to_string(), r"

        setenv FORWARD_COMPATIBLE 1
        client
        server-poll-timeout 4
        nobind
        remote ", &vpn_config.public_server_name, r" 1194 udp
        dev tun
        dev-type tun
        remote-cert-tls server
        setenv opt tls-version-min 1.0 or-highest
        reneg-sec 604800
        sndbuf 0
        rcvbuf 0
        verb 3
        setenv PUSH_PEER_INFO
        auth SHA256
        pull
        
        # Everything below this line is generated by easy-rsa INLINE CONFIG
        ", inline_tls_auth, r"
    ");

    // Save the client config
    tokio::fs::write(&path, &rval)
        .await
        .change_context(ErrorStack::WriteConfig)?;

    Ok(path)
}

pub async fn revoke(
    app: &crate::Application,
    username: &str,
) -> error_stack::Result<(), ErrorStack> {
    let path = get_ovpn_path(app, username).await?;

    crate::ca::revoke_client(app, username)
        .await
        .change_context(ErrorStack::RevokeClient)?;

    // remove the client config
    if path.exists() {
        tokio::fs::remove_file(&path)
            .await
            .change_context(ErrorStack::RemoveClientConfig)?;
    }

    // remove corresponding symlink
    let client_config_filename = match path.file_name() {
        Some(filename) => filename,
        None => {
            error_stack::bail!(ErrorStack::GetConfigFilename);
        }
    };

    let symlink_path = app.ace_db_app.vpn_path.join(client_config_filename);

    tokio::fs::remove_file(&symlink_path)
        .await
        .change_context(ErrorStack::RemoveSymlink)?;

    Ok(())
}

async fn get_ovpn_path(
    app: &crate::Application,
    username: &str,
) -> error_stack::Result<PathBuf, ErrorStack> {
    let private_subdomain_name =
        ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?
            .private_subdomain_name;

    Ok(app
        .ace_db_app
        .vpn_path
        .join(format!("{username}@{private_subdomain_name}.ovpn")))
}
