use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAwsRdss,
    GetSecurityGroups,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for aws_rds_instance in ace_graph::aws_rds::select(ace_graph::AwsRdsFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetAwsRdss)?
    {
        let resource_id = aws_rds_instance.graphkey.serialize_dashed();
        let parameter_group_resource = format!("{}-pg", resource_id);
        let parameter_group_name = format!("{}-parameter-group", aws_rds_instance.name);
        let parameter_family = aws_rds_instance.parameter_family.clone();
        let parameter_group_tag_name = format!("{}-parameter-group", aws_rds_instance.name);

        let security_groups = ace_graph::aws_vpc_sg::select(
            &ace_graph::AwsVpcSecurityGroupFilter::One(ace_graph::AwsVpcSg::AwsRds(
                aws_rds_instance.graphkey.clone(),
            )),
            ace_db_app,
        )
        .await
        .change_context(ErrorStack::GetSecurityGroups)?
        .into_iter()
        .map(|sg| {
            format!(
                "aws_security_group.{}.id",
                sg.to_terraform_resource_id(config)
            )
        })
        .collect::<Vec<_>>();

        let sng_gk = ace_graph::AwsRdsSng::AwsRds(aws_rds_instance.graphkey.clone());
        let subnet_group_name = sng_gk.serialize_dashed();

        let params = aws_rds_instance
            .parameters
            .iter()
            .map(|(name, value)| {
                format!(
                    "parameter {{\n  name = {}\n  value = {}\n}}\n",
                    JE!(name),
                    JE!(value)
                )
            })
            .collect::<Vec<_>>()
            .join("\n");

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_db_parameter_group" "#, JE!(parameter_group_resource), r#" {
                name   = "#, JE!(parameter_group_name), r#"
                family = "#, JE!(parameter_family), r#"
                
                "#, params, r#"

                tags = {
                    Name = "#, JE!(parameter_group_tag_name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                }
            }

            resource "aws_db_instance" "#, JE!(resource_id), r#" {
                identifier           = "#, JE!(aws_rds_instance.name), r#"
                allocated_storage    = "#, JE!(aws_rds_instance.allocated_storage), r#"
                storage_type         = "#, JE!(aws_rds_instance.storage_type), r#"
                engine               = "#, JE!(aws_rds_instance.engine), r#"
                engine_version       = "#, JE!(aws_rds_instance.engine_version), r#"
                instance_class       = "#, JE!(aws_rds_instance.instance_class), r#"
                db_subnet_group_name = "#, JE!(subnet_group_name), r#"
                parameter_group_name = aws_db_parameter_group."#, parameter_group_resource, r#".name
                multi_az             = "#, JE!(aws_rds_instance.multi_az), r#"
                vpc_security_group_ids = ["#, (security_groups.join(",")), r#"]
                username             = "#, JE!(aws_rds_instance.username), r#"
                password             = "#, JE!(aws_rds_instance.password), r#"
                skip_final_snapshot  = false
                backup_retention_period = 7
                backup_window          = "03:00-03:30"
                copy_tags_to_snapshot  = true

                lifecycle {
                    prevent_destroy = true
                    ignore_changes = [engine_version, allocated_storage, instance_class, storage_type]
                }

                tags = {
                    Name = "#, JE!(aws_rds_instance.name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                }
            }

            output "#, (format!("{}-endpoint", resource_id)), r#" {
                value = aws_db_instance."#, (resource_id), r#".endpoint
            }
        "#));

        output.add(
            &format!("{}-endpoint", resource_id),
            &format!("aws_db_instance.{}.endpoint", resource_id),
        );
    }

    Ok(())
}
