use super::Output;
use error_stack::ResultExt;
use garbage::{CNS<PERSON>, J<PERSON>, J<PERSON>};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetPeercons,
    SelectSubnets,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let peercon_list = ace_graph::peercon::select(&ace_graph::PeerconFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetPeercons)?;

    // Eventual TODO?:
    // Have separate rtb.tf and ngw.tf files/ace_graph selection functions

    // Create local references for convenience below
    let account_key = &config.account_key;

    let subnets = ace_graph::sn::select(ace_graph::SubnetFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::SelectSubnets)?;

    for subnet in subnets {
        // !! DO NOT USE GRAPHKEYS FOR RESOURCE NAMES HERE !!
        //        ! IT WILL BREAK INFRASTRUCTURE !

        let (include_peering_connection_routes, include_nat_gateways_if_allowed) =
            match subnet.graphkey {
                ace_graph::Subnet::PublicA
                | ace_graph::Subnet::PublicB
                | ace_graph::Subnet::PublicC => (true, true),
                ace_graph::Subnet::Temporal | ace_graph::Subnet::Vpn | ace_graph::Subnet::Ace => {
                    (true, false)
                }

                // Neither are included for private subnets
                ace_graph::Subnet::PrivateA
                | ace_graph::Subnet::PrivateB
                | ace_graph::Subnet::PrivateC => (false, false),
            };

        let is_private_a_b_c = matches!(
            subnet.graphkey,
            ace_graph::Subnet::PrivateA | ace_graph::Subnet::PrivateB | ace_graph::Subnet::PrivateC
        );

        // Map the subnet name from graphkey to original naming convention
        let mapped_subnet_suffix = subnet.graphkey.to_old_terraform_resource_suffix();

        // Map the other resources to original naming convention based on Private A, B, C, or not.
        let (subnet_resource, route_table_resource, subnet_association_resource, ngw, eip) =
            if include_peering_connection_routes {
                let sn_resource = subnet.graphkey.to_old_terraform_resource_name();
                let rtb_resource = format!("rtb-{}", &mapped_subnet_suffix);
                let sna_resource = format!("sna-{}", &mapped_subnet_suffix);
                let ngw = format!("ngw-{}-private", &mapped_subnet_suffix);
                let eip = format!("eip-{}", &ngw);

                (sn_resource, rtb_resource, sna_resource, ngw, eip)
            } else {
                let sn_resource = format!("sn-{}-private", &mapped_subnet_suffix);
                let rtb_resource = format!("rtb-{}-private", &mapped_subnet_suffix);
                let sna_resource = format!("sna-{}-private", &mapped_subnet_suffix);
                let ngw = format!("ngw-{}-private", &mapped_subnet_suffix);
                let eip = format!("eip-{}", &ngw);

                (sn_resource, rtb_resource, sna_resource, ngw, eip)
            };

        let (vpc_id, depends_on) = match subnet.vpc_id {
            ace_graph::sn::VpcId::VpcId(vpc_id) => (vpc_id, false),
            ace_graph::sn::VpcId::DeterminedByAws => ("aws_vpc.vpc_main.id".to_string(), true),
        };

        // ONLY create private subnets if NAT gateways are to be created
        if is_private_a_b_c && !config.create_nat_gateways {
            continue;
        }

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            // ------------------------------
            // Create subnet "#, JE!(&subnet_resource), r#"
            resource "aws_subnet" "#, JE!(&subnet_resource), r"{
                vpc_id = ", &vpc_id, r"
                cidr_block = ", JE!(&subnet.cidr_block.to_string()), r"
                availability_zone = ", JE!(&subnet.availability_zone), r"
                map_public_ip_on_launch = ", JE!(&subnet.map_public_ip_on_launch), r"
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &subnet_resource)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
                "#, if depends_on {
                    r"depends_on = [aws_vpc.vpc_main]"
                } else {""}, r#"
            }

            // route tables for subnet

            resource "aws_route_table" "#, JE!(&route_table_resource), r#" {
                vpc_id = aws_vpc.vpc_main.id
                route {
                    cidr_block = "0.0.0.0/0"
                    "#, if is_private_a_b_c {
                        // Only PRIVATE A, B, C get `nat_gateway_id`
                        CNSL!(r"
                        nat_gateway_id = aws_nat_gateway.", &ngw, r".id
                        ")
                    } else {
                        CNSL!(r"
                        gateway_id = aws_internet_gateway.igw_main.id
                        ")
                    }, r"
                }
            ", if include_peering_connection_routes {
                    JN!(&peercon_list, |peercon| CNSL!(r"
                    route {
                        cidr_block = ", JE!(&peercon.remote.cidr_block), r"
                        vpc_peering_connection_id = ", JE!(&peercon.peering_connection_id), r"
                    }
                "))
            } else { r"".to_string() }, r"                 
                tags = {
                    Name = ", JE!(format!("{}-{}", &account_key, &route_table_resource)), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }

            // route table association
            resource "aws_route_table_association" "#, JE!(&subnet_association_resource), r" {
                subnet_id = aws_subnet.", &subnet_resource, r".id
                route_table_id = aws_route_table.", &route_table_resource, r".id
            }", if !config.create_nat_gateways{
                r"
                
                ".to_string()
            } else {"".to_string()}, r"
        "));

        if config.create_nat_gateways && include_nat_gateways_if_allowed {
            #[rustfmt::skip]
            output.write(
                &None,
                &CNSL!(
                    r#"
                // Allocate an elastic IP for the NAT gateway
                resource "aws_eip" "#, JE!(&eip), r#" {
                    domain = "vpc"

                    tags = {
                        Name = "#, JE!(format!("{}-{}", &account_key, &eip)), r#"
                        Source = "Terraform"
                        AccountKey = "#, JE!(&account_key), r#"
                    }
                }
                
                // Create a NAT gateway in the subnet
                resource "aws_nat_gateway" "#, JE!(&ngw), r" {
                    allocation_id = aws_eip.", &eip , r".id
                    subnet_id = aws_subnet.", &subnet_resource, r".id

                    tags = {
                        Name = ", JE!(format!("{}-{}", &account_key, &ngw)), r#"
                        Source = "Terraform"
                        AccountKey = "#, JE!(&account_key), r"
                    }
                }



            "));
        }
    }

    Ok(())
}
