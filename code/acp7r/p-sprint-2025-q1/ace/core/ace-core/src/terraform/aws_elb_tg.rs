use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAwsElbTargetGroups,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for tg in ace_graph::aws_elb_tg::select(ace_graph::AwsElbTgFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetAwsElbTargetGroups)?
    {
        let resource_id = tg.graphkey.serialize_dashed();

        let protocol = tg.protocol.to_string();

        // Generate the target group
        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_lb_target_group" "#, JE!(resource_id), r#" {
                name = "#, JE!(tg.name), r#"
                port = "#, JE!(tg.port), r#"
                protocol = "#, JE!(protocol), r#"
                vpc_id = aws_vpc.vpc_main.id

                tags = {
                    Name = "#, JE!(tg.name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                }
            }
        "#));

        // Add outputs
        output.add(
            &format!("{}_arn", resource_id),
            &format!("aws_lb_target_group.{}.arn", resource_id),
        );
        output.add(
            &format!("{}_name", resource_id),
            &format!("aws_lb_target_group.{}.name", resource_id),
        );
    }

    Ok(())
}
