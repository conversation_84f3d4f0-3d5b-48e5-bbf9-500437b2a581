use error_stack::ResultExt;
use reqwest;
use serde::Deserialize;
use serde_json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    Deserialize,
    DigCommandError(std::process::ExitStatus, String),
    DigTheAuthoritativeNameserver(String),
    DigTheDomain(String),
    GetResponse,
    GetResponseText,
    UnexpectedOutputFormat(String),
}

pub type NsValues = Vec<String>;

#[derive(Deserialize, Debug)]
pub struct DNSResponse {
    #[serde(rename = "Status")]
    pub status: u8,
    #[serde(rename = "TC")]
    pub tc: bool,
    #[serde(rename = "RD")]
    pub rd: bool,
    #[serde(rename = "RA")]
    pub ra: bool,
    #[serde(rename = "AD")]
    pub ad: bool,
    #[serde(rename = "CD")]
    pub cd: bool,
    #[serde(rename = "Question")]
    pub question: Vec<Question>,
    #[serde(rename = "Answer")]
    pub answer: Option<Vec<Answer>>,
}

#[derive(Deserialize, Debug)]
pub struct Question {
    #[serde(rename = "name")]
    pub name: String,
    #[serde(rename = "type")]
    pub r#type: u8,
}

#[derive(Deserialize, Debug)]
pub struct Answer {
    #[serde(rename = "name")]
    pub name: String,
    #[serde(rename = "type")]
    pub r#type: u8,
    #[serde(rename = "TTL")]
    pub ttl: u32,
    #[serde(rename = "data")]
    pub data: String,
}

pub async fn get_dns_response(domain: &String) -> error_stack::Result<DNSResponse, ErrorStack> {
    let url = format!("https://dns.google/resolve?name={domain}&type=NS");
    let r = reqwest::get(&url)
        .await
        .change_context(ErrorStack::GetResponse)?;
    let response = r.text().await.change_context(ErrorStack::GetResponseText)?;
    let dns_response: DNSResponse =
        serde_json::from_str(&response).change_context(ErrorStack::Deserialize)?;

    Ok(dns_response)
}

pub async fn get_ns_values(domain: &str) -> error_stack::Result<NsValues, ErrorStack> {
    let dns_response = get_dns_response(&domain.to_string()).await?;

    Ok(match dns_response.answer {
        Some(answer) => {
            let mut ns_values = Vec::new();
            for record in answer {
                //Get rid of the silly extra period at the end of the ns record before returning
                ns_values.push(record.data.trim_end_matches('.').to_string());
            }
            ns_values
        }

        None => NsValues::new(),
    })
}

/// Runs `dig <domain> NS +short` and chops the "." off the end of each line.
pub async fn dig_ns_records_short(domain: &str) -> error_stack::Result<NsValues, ErrorStack> {
    let mut cmd = tokio::process::Command::new("dig");
    cmd.arg(domain).arg("NS").arg("+short");

    let output = cmd
        .output()
        .await
        .change_context(ErrorStack::DigTheDomain(domain.to_string()))?;
    let stdout = String::from_utf8_lossy(&output.stdout);

    let mut ns_records: Vec<String> = stdout
        .lines()
        .map(|s| s.trim_end_matches('.').to_string())
        .collect();

    ns_records.sort();

    Ok(ns_records)
}

/// Specifically runs `dig @<authoritative_nameserver> <subdomain> NS +noall +authority`
/// Splits output by whitespace and collects the NS records from the end of the line.
///
/// Dig output expected to split to five parts:
/// 1. Subdomain
/// 2. TTL
/// 3. Class
/// 4. Type
/// 5. NS Record
pub async fn dig_authority_ns(
    subdomain: &str,
    authoritative_nameserver: &str,
) -> error_stack::Result<NsValues, ErrorStack> {
    let mut cmd = tokio::process::Command::new("dig");
    cmd.arg(format!("@{authoritative_nameserver}"))
        .arg(subdomain)
        .arg("NS")
        .arg("+noall")
        .arg("+authority");
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdout(std::process::Stdio::inherit());

    let output = cmd
        .output()
        .await
        .change_context(ErrorStack::DigTheAuthoritativeNameserver(
            subdomain.to_string(),
        ))?;
    if !output.status.success() {
        let stderr_as_string = String::from_utf8_lossy(&output.stderr);
        error_stack::bail!(ErrorStack::DigCommandError(
            output.status,
            stderr_as_string.to_string()
        ));
    }

    let stdout = match String::from_utf8(output.stdout) {
        Ok(s) => s,
        Err(e) => {
            error_stack::bail!(ErrorStack::UnexpectedOutputFormat(e.to_string()));
        }
    };

    let mut ns_records = vec![];

    let lines = stdout.lines();
    for line in lines {
        // Split by whitespace
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() != 5 {
            error_stack::bail!(ErrorStack::UnexpectedOutputFormat(stdout.to_string()));
        } else {
            let ns_record = parts[4].trim_end_matches('.');
            ns_records.push(ns_record.to_string());
        }
    }

    ns_records.sort();

    Ok(ns_records)
}
