impl approck::server::App for crate::AppStruct {
    fn webserver_system(&self) -> &approck::server::Module {
        &self.webserver
    }

    async fn handle_api(
        &'static self,
        identity: &std::sync::Arc<Self::Identity>,
        api: &str,
        input: granite::JsonValue,
    ) -> granite::JsonValue {
        crate::libλ::api(self, identity, api, input).await
    }

    async fn webserver_route(
        &'static self,
        req: approck::server::Request,
    ) -> granite::Result<approck::server::response::Response> {
        crate::libλ::router(self, req).await
    }
}
