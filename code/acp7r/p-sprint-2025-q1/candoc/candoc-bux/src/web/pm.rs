#[approck::http(GET /pm; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Project Management Processes");

        doc.add_body(html!(
            panel {
                header {
                    h3 {"Project Management Processes"}
                }
                content {
                   h4 { "Understanding Project Management" }
                   p { "This page explains the basic steps involved in running a project from start to finish. It breaks down project management into simple stages to help you understand how projects are organized and managed." }
                   p { "By the end of this page, you'll know:" }
                    ul {
                        li { "Getting Started: How a project begins." }
                        li { "Making a Plan: Figuring out what needs to be done and how." }
                        li { "Doing the Work: Actually carrying out the plan." }
                        li { "Keeping Things on Track: Checking progress and making changes if needed." }
                        li { "Finishing Up: Officially closing the project." }
                    }
                }
            }
        ));
        Response::HTML(doc.into())
    }
}
