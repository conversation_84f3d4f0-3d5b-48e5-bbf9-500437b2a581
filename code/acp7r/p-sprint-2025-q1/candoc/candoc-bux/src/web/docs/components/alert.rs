#[approck::http(GET /docs/components/alert; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Alerts" }
                            p { "Alerts are used to draw attention to important information or to provide feedback to users, such as success messages, warnings, errors, or informational messages." }

                            h2 { "Contextual classes" }
                            p {"For proper styling, it is " b{"required"} " to use one of the six provided contextual classes."}
                            panel {
                                content {
                                    alert.primary role="alert" {"A simple primary alert—check it out!"}
                                    alert.success role="alert" {"A simple success alert—check it out!"}
                                    alert.danger role="alert" {"A simple danger alert—check it out!"}
                                    alert.warning role="alert" {"A simple warning alert—check it out!"}
                                    alert.info role="alert" style="margin-bottom: 0;"{"A simple info alert—check it out!"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "alert.primary role=\"alert\" {\"A simple primary alert—check it out!\"}\n\n"
                                            "alert.success role=\"alert\" {\"A simple success alert—check it out!\"}\n\n"
                                            "alert.danger role=\"alert\" {\"A simple danger alert—check it out!\"}\n\n"
                                            "alert.warning role=\"alert\" {\"A simple warning alert—check it out!\"}\n\n"
                                            "alert.info role=\"alert\" {\"A simple info alert—check it out!\"}"
                                        }
                                    }
                                }
                            }

                            h3 { "Conveying meaning to assistive technologies" }
                            p { "Since color is a visual indicator, ensure that meaning conveyed by color is clear from the content itself. Otherwise, provide additional context through alternative means, such as visually hidden text or the use of ARIA attributes." }

                            h2 { "Link color" }
                            p { "Links within alerts are automatically styled to match the alert color." }
                            panel {
                                content {
                                    alert.primary role="alert" {"A simple primary alert with an " a href="" { "example link" } "."}
                                    alert.success role="alert" {"A simple success alert with an " a href="" { "example link" } "."}
                                    alert.danger role="alert" {"A simple danger alert with an " a href="" { "example link" } "."}
                                    alert.warning role="alert" {"A simple warning alert with an " a href="" { "example link"} "."}
                                    alert.info role="alert" style="margin-bottom: 0;"{"A simple info alert with an " a href="" { "example link" } "."}
                                }
                                footer {
                                    pre {
                                        code {
                                            "alert.primary role=\"alert\" {\"A simple primary alert with an \" a href=\"\" { \"example link\" } \".\"}\n\n"
                                            "alert.success role=\"alert\" {\"A simple success alert with an \" a href=\"\" { \"example link\" } \".\"}\n\n"
                                            "alert.danger role=\"alert\" {\"A simple danger alert with an \" a href=\"\" { \"example link\" } \".\"}\n\n"
                                            "alert.warning role=\"alert\" {\"A simple warning alert with an \" a href=\"\" { \"example link\" } \".\"}\n\n"
                                            "alert.info role=\"alert\" {\"A simple info alert with an \" a href=\"\" { \"example link\" } \".\"}"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Contextual classes" }
                                }
                                li {
                                    a href="" { "Link color" }
                                }
                                li {
                                    a href="" { "Dismissable" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
