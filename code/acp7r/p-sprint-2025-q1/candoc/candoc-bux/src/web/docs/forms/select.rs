#[approck::http(GET /docs/forms/select; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Select input" }
                            p { "Select inputs allow users to choose a single value from a predefined list of options." }

                            h2 { "Nilla" }
                            p { "This is a basic select component that can be used for any general-purpose dropdown where you want to provide your own options, without any specialized behavior or predefined values. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "nilla_select" }
                                }
                                li {
                                    code { "nilla_select_with_help" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::select::nilla::nilla_select("nilla_example", "Select label", &[("first", "First Option"), ("second", "Second Option"), ("third", "Third Option")], None))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::select::nilla::nilla_select(\n"
                                            "    \"nilla_example\",\n"
                                            "    \"Select label\",\n"
                                            "    &[\n"
                                            "        (\"first\", \"First Option\"),\n"
                                            "        (\"second\", \"Second Option\"),\n"
                                            "        (\"third\", \"Third Option\"),\n"
                                            "    ],\n"
                                            "    None,\n"
                                            "))"
                                        }
                                    }
                                }
                            }
                            h2 { "State" }
                            p { "This is a specialized select input component that provides a pre-populated dropdown list of US states. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "state_input" }
                                }
                                li {
                                    code { "state_input_help" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::select::state::state_input("state_example", "Select state label", Some("PA".to_string())))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::select::state::state_input(\n"
                                            "    \"state_example\",\n"
                                            "    \"Select state label\",\n"
                                            "    Some(\"PA\".to_string())\n"
                                            "))"                                        
                                        }
                                    }
                                }
                            }
                            h2 { "Timezone" }
                            p { "This is a specialized select input component that provides a dropdown list of time zones, organized by region (US, Canada, Europe, America, Asia, Africa, and Other). Current support includes the following use cases:" }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Nilla" }
                                }
                                li {
                                    a href="" { "State" }
                                }
                                li {
                                    a href="" { "Timezone" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
