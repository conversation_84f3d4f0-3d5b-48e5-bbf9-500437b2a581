#[approck::http(GET /docs/layout/flexbox; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Flexbox" }
                            p { "Flex container tags can be used for basic alignment needs. Use them to arrange elements in a single row or column. Unlike grid tags, which enforce specific column widths, flex containers allow child elements to maintain their natural size while providing consistent spacing." }

                            h2 { "hbox" }
                            p { "The " code { "hbox" } " tag arranges elements horizontally in a row with equal spacing between them. Enable wrapping to multiple rows using the " code { "wrap" } " class." }

                            p { b { "Examples" } }
                            panel {
                                content {
                                    hbox {
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "hbox {\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                            panel {
                                content {
                                    hbox.wrap {
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "hbox.wrap {\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                            panel {
                                content {
                                    panel style="margin-bottom: 0;" {
                                        content {
                                            p { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ultrices sem lorem, vel lobortis libero fringilla in. Nunc vitae justo quis erat commodo facilisis vel sed diam. Integer eget est id nisl mollis maximus." }
                                            hbox {
                                                panel style="margin-bottom: 0;" {
                                                    content {
                                                        p { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." } 
                                                        (bux::button::link::label_class("Button", "/", "primary block"))
                                                    }
                                                }
                                                panel style="margin-bottom: 0;" {
                                                    content {
                                                        p { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                                        (bux::button::link::label_class("Button", "/", "primary block"))
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "panel {\n"
                                            "    content {\n"
                                            "        p {...}\n"
                                            "        hbox {\n"
                                            "            panel {\n"
                                            "                content {\n"
                                            "                    p {...}\n"
                                            "                    (bux::button...)\n"
                                            "                }\n"
                                            "            }\n"
                                            "            panel {\n"
                                            "                content {\n"
                                            "                    p {...}\n"
                                            "                    (bux::button...)\n"
                                            "                }\n"
                                            "            }\n"
                                            "        }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "vbox" }
                            p { "The " code { "vbox" } " tag arranges elements vertically in a column with equal spacing between them. You can nest " code { "vbox" } " inside " code { "hbox" } " (or vice versa) to create more complex layouts." }

                            p { b { "Examples" } }
                            panel {
                                content {
                                    hbox {
                                        panel style="margin-bottom: 0;" {
                                            content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin ultrices sem lorem, vel lobortis libero fringilla in. Nunc vitae justo quis erat commodo facilisis vel sed diam. Integer eget est id nisl mollis maximus." }
                                        }
                                        vbox {
                                            (bux::button::link::label_class("Button", "/", "primary"))
                                            (bux::button::link::label_class("Button", "/", "primary"))
                                            (bux::button::link::label_class("Button", "/", "primary"))
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "hbox {\n"
                                            "    panel {...}\n"
                                            "    vbox {\n"
                                            "        (bux::button::link::label_class(\"Button\", \"/\", \"primary\"))\n"
                                            "        (bux::button::link::label_class(\"Button\", \"/\", \"primary\"))\n"
                                            "        (bux::button::link::label_class(\"Button\", \"/\", \"primary\"))\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                            panel {
                                content {
                                    vbox {
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        hbox {
                                            panel style="margin-bottom: 0;" {
                                                content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                            }
                                            panel style="margin-bottom: 0;" {
                                                content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                            }
                                            panel style="margin-bottom: 0;" {
                                                content { "Lorem ipsum dolor sit amet, consectetur adipiscing elit." }
                                            }
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "vbox {\n"
                                            "    panel {...}\n"
                                            "    hbox {\n"
                                            "        panel {...}\n"
                                            "        panel {...}\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "hbox" }
                                }
                                li {
                                    a href="" { "vbox" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
