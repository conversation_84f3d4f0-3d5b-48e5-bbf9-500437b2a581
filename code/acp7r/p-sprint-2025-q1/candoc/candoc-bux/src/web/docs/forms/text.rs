#[approck::http(GET /docs/forms/text; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Text input" }
                            p { "Text inputs let users enter and edit text." }

                            h2 { "Currency" }
                            p { "The currency input is designed to handle monetary value input. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "currency_input" }
                                }
                                li {
                                    code { "currency_input_with_help" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::text::currency::currency_input_with_help("currency_example", "Currency label", None, "This is some descriptive help text."))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::text::currency::currency_input_with_help(\n"
                                            "    \"currency_example\",\n"
                                            "    \"Currency label\",\n"
                                            "    None,\n"
                                            "    \"This is some descriptive help text.\")\n"
                                            ")"
                                        }
                                    }
                                }
                            }
                            h2 { "String" }
                            p { "A single-line text input. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "name_label_value" }
                                }
                                li {
                                    code { "name_label_value_help" }
                                }
                                li {
                                    code { "first_name" }
                                }
                                li {
                                    code { "name_label_readonly" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::text::string::name_label_value("string_example", "String label", None))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::text::string::name_label_value(\n"
                                            "    \"string_example\",\n"
                                            "    \"String label\",\n"
                                            "    None)\n"
                                            ")"
                                        }
                                    }
                                }
                            }

                            h2 { "Textarea" }
                            p { "A multi-line text input. Current support includes the following use cases:" }
                            ul {
                                li {
                                    code { "bux_textarea" }
                                }
                                li {
                                    code { "bux_textarea_with_help" }
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (bux::input::textarea::string::name_label_value("textarea_example", "Textarea label", None))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::input::textarea::string::name_label_value(\n"
                                            "    \"textarea_example\",\n"
                                            "    \"Textarea label\",\n"
                                            "    None)\n"
                                            ")"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Currency" }
                                }
                                li {
                                    a href="" { "String" }
                                }
                                li {
                                    a href="" { "Textarea" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
