pub mod components;
pub mod forms;
pub mod index;
pub mod layout;

pub fn render_docs_nav() -> maud::Markup {
    use maud::html;

    html!(
        docs-nav {
            ul {
                li {
                    "Layout"
                    ul {
                        li { a href="/" { "Containers" } }
                        li { a href="/docs/layout/column" { "Columns" } }
                        li { a href="/docs/layout/flexbox" { "Flexbox" } }
                    }
                }
                li {
                    "Forms"
                    ul {
                        li { a href="/docs/forms/checkbox" { "Checkbox" } }
                        li { a href="/docs/forms/select" { "Select" } }
                        li { a href="/docs/forms/text" { "Text" } }
                    }
                }
                li {
                    "Components"
                    ul {
                        li { a href="/docs/components/alert" { "Alerts" } }
                        li { a href="/docs/components/button" { "Buttons" } }
                        li { a href="/docs/components/label" { "Labels" } }
                        li { a href="/docs/components/listgroup" { "List group" } }
                        li { a href="/docs/components/panel" { "Panels" } }
                    }
                }
                li {
                    "Accessibility"
                    ul {
                        li { a href="" { "ARIA attributes" } }
                    }
                }
            }
        }
    )
}
