# Workspace.Task.App.Add (aka "Add a New App")

**Description:** This workflow describes how to create a new top-level application within the `acp7r` monorepo. This is a detailed guide based on the `aplay` application. Replace `appcove/aplay` with `{containing_folder}/{app_name}`.

## 1. Create Project Directory

Create a new directory for your application inside a suitable folder (e.g., `appcove`).

```bash
mkdir -p appcove/aplay
```

## 2. Create the Main Application Crate

### `/{containing_folder}/{app_name}/Cargo.toml`

This file defines the application crate, its metadata, and dependencies.

**Example:** `appcove/aplay/Cargo.toml`

```toml
[package]
name = "aplay"
version = "0.1.0"
edition = "2021"

[package.metadata.acp]
app.port = 3021
extends = ["approck", "bux", "granite"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
```

**Rationale:**

- `[package.metadata.acp]`: This section is crucial for the `acp` build system.
  - `app.port`: Assigns a unique port for the application. Find an unused port by searching for `app.port` in the codebase.
  - `extends`: Lists the core frameworks and modules the application depends on. A minimal application will extend `approck`, `bux`, and `granite`.
- `[dependencies]`: Specifies the crate dependencies. Note that core libraries like `approck` are included as path dependencies.

### `/{containing_folder}/{app_name}/src/main.rs`

This is the entry point of the application. It should contain only the `approck::main!` macro.

**Example:** `appcove/aplay/src/main.rs`

```rust
approck::main!(aplay);
```

### `/{containing_folder}/{app_name}/src/lib.rs`

This file contains the core application logic, including configuration, application state, and identity management.

**Example:** `appcove/aplay/src/lib.rs`

```rust
// ----------------------------------------------------------------------------------------------
// Imports
pub mod module;
pub mod web;

#[path = "libλ.rs"]
pub mod libλ;

// ----------------------------------------------------------------------------------------------
// Create application

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
}

#[derive(Debug)]
pub enum IdentityStruct {
    Anonymous,
}

pub use crate::web::Document::Document as DocumentStruct;

// ----------------------------------------------------------------------------------------------
// This app is also used for web and api requests

pub trait App: approck::App + approck::server::App {}
pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

// ----------------------------------------------------------------------------------------------
// Implement application

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }

    async fn init(&self) -> granite::Result<()> {
        approck::Module::init(&self.webserver).await?;
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct::Anonymous)
    }
}

impl approck::Identity for IdentityStruct {}

// ----------------------------------------------------------------------------------------------
// Implement traits

impl App for AppStruct {}
impl Identity for IdentityStruct {}
```

**Rationale:**

- `AppConfig`: Defines the application's configuration structure. For a simple app, this just contains the webserver configuration.
- `AppStruct`: Represents the application's state.
- `IdentityStruct`: Defines user identity. For a plain app, this is just `Anonymous`.
- `approck::App` implementation:
  - `new`: Initializes the application state from the configuration.
  - `init`: Performs asynchronous initialization.
  - `auth`: Handles authentication. A plain app has no authentication.

## 3. Implement Modules

### `/{containing_folder}/{app_name}/src/module/mod.rs`

This file declares the modules used by the application.

**Example:** `appcove/aplay/src/module/mod.rs`

```rust
pub mod approck_server;
```

### `/{containing_folder}/{app_name}/src/module/approck_server.rs`

This file implements the `approck::server::App` trait, which is responsible for routing and handling HTTP requests.

**Example:** `appcove/aplay/src/module/approck_server.rs`

```rust
impl approck::server::App for crate::AppStruct {
    fn webserver_system(&self) -> &approck::server::Module {
        &self.webserver
    }

    async fn handle_api(
        &'static self,
        identity: &std::sync::Arc<Self::Identity>,
        api: &str,
        input: granite::JsonValue,
    ) -> granite::JsonValue {
        crate::libλ::api(self, identity, api, input).await
    }

    async fn webserver_route(
        &'static self,
        req: approck::server::Request,
    ) -> granite::Result<approck::server::response::Response> {
        crate::libλ::router(self, req).await
    }
}

impl approck::server::Identity for crate::IdentityStruct {}
```

**Rationale:**

- The implementation delegates API handling and routing to the generated `libλ` module.

## 4. Implement Web Interface

### `/{containing_folder}/{app_name}/src/web/mod.rs`

Declares the web-related modules.

**Example:** `appcove/aplay/src/web/mod.rs`

```rust
#[allow(non_snake_case)]
pub mod Document;

pub mod index;
```

### `/{containing_folder}/{app_name}/src/web/index.rs`

This file defines the handler for the root URL (`/`).

**Example:** `appcove/aplay/src/web/index.rs`

```rust
#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("aplay");

        doc.add_body(html!(

            h1 style="text-align: center; margin-top: 7em;" {
                "Welcome to aplay"
            }
        ));

        Response::HTML(doc.into())
    }
}
```

**Rationale:**

- `#[approck::http(...)]`: This macro defines an HTTP endpoint.
  - `GET /`: Handles GET requests to the root path.
  - `AUTH None`: No authentication is required.
  - `return HTML`: The endpoint returns an HTML response.
- The `request` function generates the HTML content for the page.

### `/{containing_folder}/{app_name}/src/web/Document.rs`

Defines the main HTML document structure using the `bux` framework.

**Example:** `appcove/aplay/src/web/Document.rs`

```rust
bux::document! {
    pub struct Document {}

    impl Document {
        pub fn new(
            _app: &'static crate::AppStruct,
            _identity: &crate::IdentityStruct,
            _req: &approck::server::Request,
        ) -> Self {
            use bux::document::Cliffy;
            use bux::document::Base;

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(_req.path());
            this.set_title("aplay"); // default title
            this.set_site_name("aplay");
            this.set_owner("AppCove, Inc.");

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }

    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::Cliffy for Document {}
    impl crate::Document for Document {}
}
```

### `/{containing_folder}/{app_name}/src/web/Document.ts`

The TypeScript counterpart for the document.

**Example:** `appcove/aplay/src/web/Document.ts`

```typescript
import { BuxDocumentCliffy } from "@bux/document/cliffy.mts";

class Document extends BuxDocumentCliffy {}

new Document();

import "./Document.mcss";
```

### `/{containing_folder}/{app_name}/src/web/Document.mcss`

The stylesheet for the document.

**Example:** `appcove/aplay/src/web/Document.mcss`

```css
/* styles for aplay */
```

## 5. Update Workspace `Cargo.toml`

Finally, add the new crate to the workspace `Cargo.toml` file in alphabetical order.

```toml
[workspace]
members = [
    # ... other members
    "appcove/aplay",
    # ... other members
]
```

## 6. Build & Verify

Run the build and check commands to ensure everything is set up correctly.

If the user did not add `[app.aplay]` to the `LOCAL.toml` you may get an error about that. The user must fix it.

```bash
./acp build -p aplay
./acp check
```
