#!/bin/bash
# Ensure db-start.sh has already run and PostgreSQL is ready

set -e

cd "$(dirname "$0")"

##########################################################

echo "Attempting to drop candoc database"
docker compose exec postgres dropdb -U app --force candoc || true

##########################################################

echo "Attempting to drop runpod database"
docker compose exec postgres dropdb -U app --force runpod || true

##########################################################

echo "Attempting to drop reo database"
docker compose exec postgres dropdb -U app --force reo || true

##########################################################

echo "Attempting to drop df4l database"
docker compose exec postgres dropdb -U app --force df4l || true

##########################################################

echo "Attempting to drop gifthopper database"
docker compose exec postgres dropdb -U app --force gifthopper || true

##########################################################

echo "Attempting to drop homewaters database"
docker compose exec postgres dropdb -U app --force homewaters || true

##########################################################

echo "Attempting to drop rrr database"
docker compose exec postgres dropdb -U app --force rrr || true

##########################################################

echo "Attempting to drop pm5 database"
docker compose exec postgres dropdb -U app --force pm5 || true

##########################################################

# purge redis
echo "Purging redis"
docker compose exec redis redis-cli flushall

echo "Complete"
