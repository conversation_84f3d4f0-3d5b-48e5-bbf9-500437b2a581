CREATE SCHEMA IF NOT EXISTS api_twilio;

CREATE TABLE api_twilio.log_api_send (
    log_api_send_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    sender_key varchar(64) NOT NULL,
    from_number varchar(32) NOT NULL,
    to_number varchar(32) NOT NULL,
    message_body text NOT NULL,
    request_data jsonb NOT NULL DEFAULT '{}'::jsonb,
    request_ts timestamptz(6) NOT NULL,
    response_ts timestamptz(6),
    response_status integer,
    response_text text,
    response_error text,
    response_data jsonb NOT NULL DEFAULT '{}'::jsonb,
    duration_ms integer,
    success boolean NOT NULL DEFAULT false,
    CONSTRAINT log_api_send_pkey PRIMARY KEY (log_api_send_uuid)
);

CREATE INDEX idx_api_twilio_log_api_send_create_ts ON api_twilio.log_api_send (create_ts);
CREATE INDEX idx_api_twilio_log_api_send_success ON api_twilio.log_api_send (success);
CREATE INDEX idx_api_twilio_log_api_send_to_number ON api_twilio.log_api_send (to_number);
CREATE INDEX idx_api_twilio_log_api_send_sender_key ON api_twilio.log_api_send (sender_key);