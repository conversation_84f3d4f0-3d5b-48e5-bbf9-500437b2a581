CREATE SCHEMA IF NOT EXISTS legal_plane;

-- Main document table containing current active version
CREATE TABLE "legal_plane"."document" (
  "document_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "document_psid" varchar(64) NULL,
  "active" boolean DEFAULT true NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "body_markdown" text NOT NULL,
  "revision" varchar(64) NOT NULL,
  CONSTRAINT document_pkey PRIMARY KEY (document_uuid)
);

CREATE UNIQUE INDEX "legal_plane.document.document_psid.index" ON "legal_plane"."document" USING btree ("document_psid") WHERE document_psid IS NOT NULL;

-- Draft versions of documents before they become active
CREATE TABLE "legal_plane"."document_draft" (
  "document_draft_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "document_psid" varchar(64) NULL,
  "document_uuid" uuid NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "body_markdown" text NOT NULL,
  "revision" varchar(64) NOT NULL,
  CONSTRAINT document_draft_pkey PRIMARY KEY (document_draft_uuid),
  CONSTRAINT "document_draft>>document" FOREIGN KEY (document_uuid) REFERENCES "legal_plane"."document" (document_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE INDEX "legal_plane.document_draft.document_psid.index" ON "legal_plane"."document_draft" USING btree ("document_psid");
CREATE INDEX "legal_plane.document_draft.document_uuid.index" ON "legal_plane"."document_draft" USING btree ("document_uuid");

-- Historical versions of documents
CREATE TABLE "legal_plane"."document_version" (
  "document_version_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "document_uuid" uuid NOT NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "name" varchar(255) NOT NULL,
  "body_markdown" text NOT NULL,
  "revision" varchar(64) NOT NULL,
  CONSTRAINT document_version_pkey PRIMARY KEY (document_version_uuid),
  CONSTRAINT "document_version>>document" FOREIGN KEY (document_uuid) REFERENCES "legal_plane"."document" (document_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE INDEX "legal_plane.document_version.document_uuid.index" ON "legal_plane"."document_version" USING btree ("document_uuid");
CREATE INDEX "legal_plane.document_version.revision.index" ON "legal_plane"."document_version" USING btree ("document_uuid", "revision");

-- Agreements/signatures on documents
CREATE TABLE "legal_plane"."agreement" (
  "agreement_uuid" uuid DEFAULT public.uuidv7() NOT NULL,
  "document_uuid" uuid NOT NULL,
  "identity_uuid" uuid NULL,
  "uuid_entity" uuid NULL,
  "create_ts" timestamptz(6) NOT NULL DEFAULT now(),
  "create_addr" inet NOT NULL,
  "create_name" varchar(255) NOT NULL,
  "name" varchar(255) NOT NULL,
  "body_markdown" text NOT NULL,
  "revision" varchar(64) NOT NULL,
  CONSTRAINT agreement_pkey PRIMARY KEY (agreement_uuid),
  CONSTRAINT "agreement>>document" FOREIGN KEY (document_uuid) REFERENCES "legal_plane"."document" (document_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "agreement>>identity" FOREIGN KEY (identity_uuid) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "agreement>>entity" FOREIGN KEY (uuid_entity) REFERENCES "auth_fence"."identity" (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE INDEX "legal_plane.agreement.document_uuid.index" ON "legal_plane"."agreement" USING btree ("document_uuid");
CREATE INDEX "legal_plane.agreement.identity_uuid.index" ON "legal_plane"."agreement" USING btree ("identity_uuid");
CREATE INDEX "legal_plane.agreement.uuid_entity.index" ON "legal_plane"."agreement" USING btree ("uuid_entity");
CREATE INDEX "legal_plane.agreement.create_ts.index" ON "legal_plane"."agreement" USING btree ("create_ts");

-- Ensure we can't have duplicate agreements for the same document/identity/entity combination
CREATE UNIQUE INDEX "legal_plane.agreement.unique_identity_entity_document.index" ON "legal_plane"."agreement" USING btree ("document_uuid", "identity_uuid", "uuid_entity", "revision") WHERE identity_uuid IS NOT NULL AND uuid_entity IS NOT NULL;