-- PM5 Initial Data
-- Sample data for development and testing

-- Note: This file contains commented-out sample data that can be used once you have
-- actual identity UUIDs from your auth_fence.identity table.
--
-- To use the sample data:
-- 1. Create users in your auth_fence system
-- 2. Get their identity UUIDs from the auth_fence.identity table
-- 3. Replace the placeholder UUIDs below with actual identity UUIDs
-- 4. Uncomment the INSERT statements

-- Sample data is commented out to prevent foreign key constraint violations
-- during initial database setup

/*
-- Sample project 1: Website Redesign
INSERT INTO pm5.project (
    project_uuid,
    name,
    description,
    status,
    start_date,
    end_date,
    budget_amount,
    budget_currency,
    created_by_identity_uuid,
    updated_by_identity_uuid
) VALUES (
    '01234567-89ab-cdef-0123-456789abcdef',
    'Website Redesign',
    'Complete redesign of the company website with modern UI/UX and improved performance',
    'Active',
    '2024-01-15',
    '2024-06-30',
    50000.00,
    'USD',
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID', -- Replace with actual identity UUID
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID'  -- Replace with actual identity UUID
);

-- Sample project 2: Mobile App Development
INSERT INTO pm5.project (
    project_uuid,
    name,
    description,
    status,
    start_date,
    end_date,
    budget_amount,
    budget_currency,
    created_by_identity_uuid,
    updated_by_identity_uuid
) VALUES (
    '01234567-89ab-cdef-0123-456789abcde0',
    'Mobile App Development',
    'Development of iOS and Android mobile applications for customer engagement',
    'Planning',
    '2024-03-01',
    '2024-12-31',
    75000.00,
    'USD',
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID', -- Replace with actual identity UUID
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID'  -- Replace with actual identity UUID
);

-- Sample project 3: Database Migration
INSERT INTO pm5.project (
    project_uuid,
    name,
    description,
    status,
    start_date,
    end_date,
    budget_amount,
    budget_currency,
    created_by_identity_uuid,
    updated_by_identity_uuid
) VALUES (
    '01234567-89ab-cdef-0123-456789abcde1',
    'Database Migration',
    'Migration from legacy database system to modern PostgreSQL infrastructure',
    'Completed',
    '2023-09-01',
    '2023-12-15',
    25000.00,
    'USD',
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID', -- Replace with actual identity UUID
    'REPLACE-WITH-ACTUAL-IDENTITY-UUID'  -- Replace with actual identity UUID
);

-- Sample tasks for Website Redesign project
INSERT INTO pm5.task (
    task_uuid,
    project_uuid,
    title,
    description,
    status,
    priority,
    due_date,
    estimated_hours,
    completion_percentage,
    created_by_identity_uuid,
    updated_by_identity_uuid,
    assigned_to_identity_uuid
) VALUES
(
    '11234567-89ab-cdef-0123-456789abcdef',
    '01234567-89ab-cdef-0123-456789abcdef',
    'Design System Creation',
    'Create a comprehensive design system with components, colors, and typography',
    'Done',
    'High',
    '2024-02-15 17:00:00+00',
    40.0,
    100,
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '01234567-89ab-cdef-0123-456789abcdef'  -- Replace with actual identity UUID
),
(
    '11234567-89ab-cdef-0123-456789abcde0',
    '01234567-89ab-cdef-0123-456789abcdef',
    'Homepage Redesign',
    'Redesign the homepage with new layout and improved user experience',
    'InProgress',
    'High',
    '2024-03-01 17:00:00+00',
    60.0,
    75,
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '01234567-89ab-cdef-0123-456789abcdef'  -- Replace with actual identity UUID
),
(
    '11234567-89ab-cdef-0123-456789abcde1',
    '01234567-89ab-cdef-0123-456789abcdef',
    'Performance Optimization',
    'Optimize website performance and loading times',
    'Todo',
    'Medium',
    '2024-04-15 17:00:00+00',
    30.0,
    0,
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    NULL
);

-- Sample task comments
INSERT INTO pm5.task_comment (
    comment_uuid,
    task_uuid,
    comment_text,
    created_by_identity_uuid
) VALUES
(
    '21234567-89ab-cdef-0123-456789abcdef',
    '11234567-89ab-cdef-0123-456789abcdef',
    'Design system has been completed and approved by stakeholders. All components are documented in Figma.',
    '01234567-89ab-cdef-0123-456789abcdef' -- Replace with actual identity UUID
),
(
    '21234567-89ab-cdef-0123-456789abcde0',
    '11234567-89ab-cdef-0123-456789abcde0',
    'Homepage mockups are ready for review. Please check the responsive design on mobile devices.',
    '01234567-89ab-cdef-0123-456789abcdef' -- Replace with actual identity UUID
);

-- Sample time entries
INSERT INTO pm5.time_entry (
    time_entry_uuid,
    task_uuid,
    identity_uuid,
    start_time,
    end_time,
    duration_minutes,
    description
) VALUES
(
    '31234567-89ab-cdef-0123-456789abcdef',
    '11234567-89ab-cdef-0123-456789abcdef',
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '2024-01-20 09:00:00+00',
    '2024-01-20 17:00:00+00',
    480,
    'Working on color palette and typography for the design system'
),
(
    '31234567-89ab-cdef-0123-456789abcde0',
    '11234567-89ab-cdef-0123-456789abcde0',
    '01234567-89ab-cdef-0123-456789abcdef', -- Replace with actual identity UUID
    '2024-02-01 10:00:00+00',
    '2024-02-01 15:30:00+00',
    330,
    'Creating homepage wireframes and initial layout concepts'
);

-- Note: Project members would typically be added here, but since we don't have
-- actual identity UUIDs, this should be done after setting up real users
-- Example:
-- INSERT INTO pm5.project_member (project_uuid, identity_uuid, role, added_by_identity_uuid)
-- VALUES ('01234567-89ab-cdef-0123-456789abcdef', 'actual-user-uuid', 'Lead', 'admin-user-uuid');
*/
