#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("AppCove PM5");

        doc.add_body(html!(
            div.container.bg-white {
                h1 { "Welcome to AppCove PM5" }

                p { "This is the AppCove PM5 application homepage." }

                hr;

                .row {
                    .col-md-6 {
                        h2 { "Features" }
                        ul {
                            li { "Project Management" }
                            li { "Task Tracking" }
                            li { "Team Collaboration" }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}
