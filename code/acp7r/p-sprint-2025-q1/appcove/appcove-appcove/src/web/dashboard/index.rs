#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(identity: Identity, doc: Document) -> Response {
        use maud::html;

        doc.set_title("Dashboard");

        // Check if user is logged in
        let is_logged_in = identity.is_logged_in();
        let user_info = if is_logged_in {
            Some((
                identity
                    .name()
                    .unwrap_or_else(|| "Unknown User".to_string()),
                identity.email().unwrap_or_else(|| "No email".to_string()),
                identity.avatar_uri(),
            ))
        } else {
            None
        };

        doc.add_body(html! {
            div.container.mt-4 {
                div.row {
                    div.col-12 {
                        h1 { "AppCove Dashboard" }

                        // Authentication Status Card
                        div.card.mt-4.dashboard-welcome {
                            div.card-body {
                                @if let Some((name, email, avatar_uri)) = user_info {
                                    div.d-flex.align-items-center {
                                        @if let Some(avatar) = avatar_uri {
                                            img.rounded-circle.me-3 src=(avatar) width="50" height="50";
                                        }
                                        div {
                                            h4.text-white.mb-1 { "Welcome back, " (name) "!" }
                                            p.text-white-50.mb-0 { (email) }
                                        }
                                    }
                                    p.text-white.mt-3 { "You are successfully logged in to the AppCove application." }
                                } @else {
                                    h4.text-white { "Welcome to AppCove" }
                                    p.text-white { "Please sign in to access all features of the application." }
                                    a.btn.btn-light.btn-lg href="/auth/" { "Sign In with Google" }
                                }
                            }
                        }

                        div.card.mt-4.feature-card {
                            div.card-header {
                                h5.card-title { "Application Features" }
                            }
                            div.card-body {
                                p.card-text { "This AppCove application includes:" }
                                ul {
                                    li { "🔐 Google OAuth authentication via auth-fence" }
                                    li { "🗄️ PostgreSQL database integration" }
                                    li { "⚡ Redis caching for performance" }
                                    li { "🎨 Modern UI with Bootstrap and Bux components" }
                                    li { "📱 Responsive design for all devices" }
                                }
                            }
                        }

                        @if is_logged_in {
                            div.card.mt-4.feature-card {
                                div.card-header {
                                    h5.card-title { "Authenticated Features" }
                                }
                                div.card-body {
                                    p.card-text { "Now that you're logged in, you can access:" }
                                    ul {
                                        li { "👤 User profile management" }
                                        li { "🔧 Account settings" }
                                        li { "📊 Team collaboration tools" }
                                        li { "🔒 Secure data access" }
                                    }
                                    div.mt-3 {
                                        a.btn.btn-primary.me-2 href="/myaccount/" { "My Account" }
                                        a.btn.btn-outline-secondary href="/auth/logout" { "Sign Out" }
                                    }
                                }
                            }
                        } @else {
                            div.card.mt-4.feature-card {
                                div.card-header {
                                    h5.card-title { "Get Started" }
                                }
                                div.card-body {
                                    p.card-text { "To access all features:" }
                                    ol {
                                        li { "Click the 'Sign In with Google' button above" }
                                        li { "Authorize the application with your Google account" }
                                        li { "Return to this dashboard to see your personalized content" }
                                    }
                                    p.text-muted.small { "Your data is secure and we only access basic profile information." }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
