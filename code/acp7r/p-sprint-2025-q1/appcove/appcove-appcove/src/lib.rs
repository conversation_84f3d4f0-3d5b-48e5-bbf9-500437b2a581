#[path = "libλ.rs"]
pub mod libλ;

mod module;
mod web;

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
    pub auth_fence_provider: auth_fence_provider::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub postgres: approck_postgres::ModuleStruct,
    pub redis: approck_redis::ModuleStruct,
    pub auth_fence: auth_fence::types::ModuleStruct,
    pub auth_fence_provider: auth_fence_provider::ModuleStruct,
}

#[derive(Debug)]
pub struct AuthFenceProviderTokenIdentity {
    pub scope_email: bool,
    pub scope_profile: bool,
}

#[derive(Debug)]
pub struct IdentityStruct {
    pub request: RequestIdentity,
    pub auth_fence: Option<auth_fence::api::identity::Identity>,
    pub auth_fence_provider: Option<AuthFenceProviderTokenIdentity>,
}

#[derive(Debug, Clone)]
pub struct RequestIdentity {
    pub remote_address: std::net::IpAddr,
    pub session_token: String,
}

pub use crate::web::Document::Document as DocumentStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres)?,
            redis: approck_redis::ModuleStruct::new(config.redis)?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence)?,
            auth_fence_provider: auth_fence_provider::ModuleStruct::new(
                config.auth_fence_provider,
            )?,
        })
    }

    async fn init(&self) -> granite::Result<()> {
        use approck::Module;
        self.redis.init().await?;
        self.postgres.init().await?;
        self.webserver.init().await?;
        self.auth_fence.init().await?;
        self.auth_fence_provider.init().await?;
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {}", crate_name);
        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use auth_fence::App;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        let request = RequestIdentity {
            remote_address: req.remote_ip(),
            session_token: req.session_token(),
        };

        let auth_fence: Option<auth_fence::api::identity::Identity> = (auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await)
            .unwrap_or_default();

        Ok(IdentityStruct {
            request,
            auth_fence,
            auth_fence_provider: None,
        })
    }
}

///////////////////////////////////////////////////////////////////////////////////////////////////

// Trait definitions for web pages
pub trait App:
    approck::App
    + approck::server::App
    + approck_postgres::App
    + approck_redis::App
    + auth_fence::App
    + auth_fence_provider::App
{
}

pub trait Identity:
    approck::Identity + auth_fence::Identity + auth_fence_provider::Identity + bux::Identity
{
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document:
    bux::document::Base + bux::document::Cliffy + auth_fence::Document + auth_fence_provider::Document
{
}

// Implement traits for our concrete types
impl App for AppStruct {}

impl auth_fence_provider::App for AppStruct {
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct {
        &self.auth_fence_provider
    }
}

impl approck::Identity for IdentityStruct {}

impl Identity for IdentityStruct {}

impl auth_fence_provider::Identity for IdentityStruct {
    fn scope_email(&self) -> bool {
        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_email,
            None => false,
        }
    }
    fn scope_profile(&self) -> bool {
        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_profile,
            None => false,
        }
    }
}

impl Document for DocumentStruct {}
