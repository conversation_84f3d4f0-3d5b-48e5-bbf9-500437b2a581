            about-appcove {
                #page-heading {
                     .grid-12 {
                        display: flex;
                        flex-wrap: wrap;
                        margin: 0 -1rem;
                        position: relative;
                        max-width: 1200px;
                        margin-inline: auto;
                    }

                    .grid-4 {
                        width: 100%;
                        max-width: 33.3333%;
                        padding: 0 1rem;
                        box-sizing: border-box;
                    }

                    /* Yellow circle background */
                    .circle-bg {
                        position: absolute;
                        width: 700px;
                        height: 700px;
                        background-color: #fff9c4;
                        border-radius: 50%;
                        top: -150px;
                        left: -200px;
                        z-index: 1;
                    }
                    margin-bottom: 2rem;

                    h1 {
                        font-size: 3rem;
                        color: #012650f8;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: bold;
                    }

                    p {
                        font-size: 2rem;
                        color: #00bfa5;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: 600;
                    }

                    strong {
                        color: #012650f8;
                        font-weight: bold;
                    }
                    
                    .circle-image {
                        float: left;
                        width: 300px;
                        height: 300px;
                        shape-outside: circle();
                        clip-path: circle();
                        -webkit-clip-path: circle(); /* For Safari */
                        background-color: #0c2d48;
                        margin: 0 2rem 1rem 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .circle-image img {
                        width: 200px;
                        height: auto;
                        border-radius: 50%;
                        object-fit: cover;
                    }

                }
                #page-body {
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.8rem;
                        color: #103d55;;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }

                #page-body {
                    margin-top: 1rem; 
                    content{
                        height: 1px;
                        color:#444 ;
                        font-size: 1rem;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }

                #page-footer {
                    margin-top: 2rem;

                    p {
                        margin-bottom: 0.8rem;
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }

                    strong {
                        color: hwb(184 7% 49% / 0.973);
                        font-weight: bold;
                    }
                    
                    br {
                        margin-bottom: 0.8rem;
                    }    
                }
                #page-main {
                    margin-top: 2rem;
                    display: flex;
                    flex-direction: column;
                    gap: 2rem;

                    header-2 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }

                    p {
                        font-size: 1rem;
                        color: #8a8c8d;
                        line-height: 1.6;
                    }

                    br {
                        margin-bottom: 0.5rem;
                    }

                    header-3 {
                        font-size: 2rem;
                        color: #012650f8;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    
                    hr {
                        all: initial;
                        display: block;
                        border-bottom: 2.3px dotted rgb(9, 81, 129);
                    }   
                    .arrow-icon-wrapper {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 3rem;
                        height: 3rem;
                        border-radius: 80%;
                        background-color: #007b8a;
                        margin-top: 2rem;
                        margin-bottom: 3rem;
                        cursor: pointer;
                        transition: all 0.3s ease;

                    &:hover {
                        background-color: #0887a7;
                    }

                    .arrow-icon {
                        color: white;
                        font-size: 2rem;
                        transition: all 0.3s ease;
                    }
                    }
                }
                #page-heading {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    h4 {
                        font-size: 2rem;
                        color:#15c9b1;
                        margin-bottom: 1rem;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                }
                .staff-grid {
                    display: flex;
                    flex-wrap: wrap;
                    margin-top: 1rem;
                    margin-bottom: 1rem;
                }

                .staff-card {
                    flex: 0 0 calc(25% - 2rem);
                    background-color: lab(94.37% -0.71 3);
                    border-radius: 10px;
                    display: flex;
                    flex-direction: column;
                    min-height: 80px;
                    
                &:hover {
                        background-color: #e6e6e6;
                }

                strong {
                        font-size: 1.3rem;
                        color: #1f2527;
                        font-weight: bold;
                        padding-left: 1.5rem;
                }

                span {
                        font-size: 1.2rem;
                        color: #353638f8;
                        font-weight: bold;
                        padding-left: 1.5rem;
                    }

                em {
                        font-size: 1rem;
                        color:  #00bfa5;
                        margin-bottom: 1rem;
                        font-weight: bold;
                        padding-left: 1.5rem;
                    }
                }
                #page-footer {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }

                    strong {
                        font-weight: bold;
                    }
                }
                #page-body {
                    margin-top: 2rem;
                    margin-bottom: 2rem;

                    p {
                        font-size: 1.2rem;
                        color: #1f2527;
                        line-height: 1.6;
                        font-weight: bold;
                    }

                    strong  {
                        font-weight: bold;
                    }

                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                }
                #timeline-panel {
                        background-color: #e8ebe0a8;
                        border-radius: 0.5rem;
                        padding: 1rem 1.25rem;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    
                    h5 {
                        font-weight: bold;
                        color: #21536C;
                        font-size: 2.7rem;
                        line-height: 1;
                    }
                    p.notable{
                        font-size: 1.2rem;
                        color: #05475e;
                        line-height: 1.6;
                        font-weight: bold;
                    }
                    .timeline-container {
                        position: relative;
                        margin-left: 10px;
                        border-left: 2px dotted #129686;
                        padding-left: 1rem;
                        margin-top: 1rem;
                        margin-bottom: 1rem;
                    
                    }
                    .timeline-item {
                        position: relative;
                        padding-left: 1rem;
                        margin-bottom: 2rem;
                    }
                    .timeline-dot {
                        height: 15px;
                        width: 15px;
                        background-color: #119191;
                        border-radius: 50%;
                        display: inline-block;
                        margin-right: 8px;
                        position: absolute;
                        left: -7px;
                        top: 3px;
                    }
                    .timeline-year {
                        font-weight: bold;
                        color: #054f5a;
                    }
                    .active-project {
                        font-weight: 600;
                        color: #119191;
                        font-style: italic;
                    }
                    .inactive-project {
                        font-weight: 600;
                        color: #292c2e;
                        font-style: italic;
                    }
                    strong {
                        color: #01070ff8;
                        font-weight: bold;
                        font-size: 1.3rem;
                    }
                    small {
                        color: #292c2e;
                        font-weight: bold;
                        font-size: 1.2rem;
                    }
                }
                    br {
                        margin-bottom: 0.8rem;
                        margin-top: 0.8rem;
                    }
                
                    .company-logo {
                        justify-content: left;
                        align-items: left;
                        margin-top: 2rem;
                        
                    img {
                        height: 50px;
                        width: auto;
                    }
                }
            }
        
        
            
        
    
              
